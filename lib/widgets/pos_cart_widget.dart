import 'package:flutter/material.dart';
import '../models/pos_models.dart';

class PosCartWidget extends StatelessWidget {
  final Map<int, double> cart;
  final List<Product> products;
  final Function(int) onRemoveFromCart;
  final Function(int, double) onUpdateQuantity;

  const PosCartWidget({
    Key? key,
    required this.cart,
    required this.products,
    required this.onRemoveFromCart,
    required this.onUpdateQuantity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (cart.isEmpty) {
      return const Center(
        child: Text(
          'Cart is empty\nScan or search products to add them',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    final cartItems = cart.entries.map((entry) {
      final product = products.firstWhere(
        (p) => p.id == entry.key,
        orElse: () => Product(
          id: entry.key,
          name: 'Unknown Product',
          description: 'Product not found',
          price: 0.0,
          quantity: 0.0,
          isTaxed: false,
          batchNumber: '',
          cost: 0.0,
          taxRate: 0.0,
          barcode: '',
        ),
      );
      return MapEntry(product, entry.value);
    }).toList();

    return ListView.builder(
      itemCount: cartItems.length,
      itemBuilder: (context, index) {
        final product = cartItems[index].key;
        final quantity = cartItems[index].value;
        final total = product.price * quantity;

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: ListTile(
            title: Text(
              product.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Unit Price: \$${product.price.toStringAsFixed(2)}'),
                Row(
                  children: [
                    const Text('Qty: '),
                    SizedBox(
                      width: 60,
                      child: TextFormField(
                        initialValue: quantity.toString(),
                        keyboardType: TextInputType.number,
                        style: const TextStyle(fontSize: 14),
                        decoration: const InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                        ),
                        onFieldSubmitted: (value) {
                          final newQuantity = double.tryParse(value);
                          if (newQuantity != null && newQuantity > 0) {
                            onUpdateQuantity(product.id!, newQuantity);
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text('kg'),
                  ],
                ),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${total.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.green,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.remove_circle, color: Colors.red),
                  onPressed: () => onRemoveFromCart(product.id!),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}