import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CheckoutWidget extends StatelessWidget {
  final double subtotal;
  final double vatTotal;
  final double total;
  final String paymentMethod;
  final Function(String) onPaymentMethodChanged;
  final TextEditingController cashReceivedController;
  final Function() onProcessSale;
  final bool isProcessing;

  const CheckoutWidget({
    Key? key,
    required this.subtotal,
    required this.vatTotal,
    required this.total,
    required this.paymentMethod,
    required this.onPaymentMethodChanged,
    required this.cashReceivedController,
    required this.onProcessSale,
    required this.isProcessing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cashReceived = double.tryParse(cashReceivedController.text) ?? 0.0;
    final change = cashReceived - total;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Checkout',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Order Summary
            _buildSummaryRow('Subtotal:', subtotal),
            if (vatTotal > 0) _buildSummaryRow('Tax:', vatTotal),
            const Divider(),
            _buildSummaryRow('Total:', total, isTotal: true),
            
            const SizedBox(height: 16),
            
            // Payment Method
            const Text('Payment Method:', style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Cash'),
                    value: 'Cash',
                    groupValue: paymentMethod,
                    onChanged: (value) => onPaymentMethodChanged(value!),
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Card'),
                    value: 'Card',
                    groupValue: paymentMethod,
                    onChanged: (value) => onPaymentMethodChanged(value!),
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
            
            // Cash received input (only show for cash payments)
            if (paymentMethod == 'Cash') ...[
              const SizedBox(height: 16),
              TextField(
                controller: cashReceivedController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                ],
                decoration: const InputDecoration(
                  labelText: 'Cash Received',
                  prefixText: '\$',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 8),
              
              // Change calculation
              if (cashReceived > 0) ...[
                _buildSummaryRow(
                  'Change:',
                  change,
                  isChange: true,
                  isNegative: change < 0,
                ),
                if (change < 0)
                  const Text(
                    'Insufficient cash received',
                    style: TextStyle(color: Colors.red, fontSize: 12),
                  ),
              ],
            ],
            
            const SizedBox(height: 20),
            
            // Process Sale Button
            ElevatedButton(
              onPressed: _canProcessSale() ? onProcessSale : null,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: isProcessing
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('Processing...'),
                      ],
                    )
                  : const Text(
                      'PROCESS SALE',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, double amount, {bool isTotal = false, bool isChange = false, bool isNegative = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isNegative 
                  ? Colors.red 
                  : isChange 
                      ? Colors.green 
                      : null,
            ),
          ),
        ],
      ),
    );
  }

  bool _canProcessSale() {
    if (isProcessing) return false;
    if (total <= 0) return false;
    
    if (paymentMethod == 'Cash') {
      final cashReceived = double.tryParse(cashReceivedController.text) ?? 0.0;
      return cashReceived >= total;
    }
    
    return true; // For card payments
  }
}