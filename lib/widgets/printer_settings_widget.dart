import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../services/printer_service.dart';

class PrinterSettingsWidget extends StatefulWidget {
  const PrinterSettingsWidget({Key? key}) : super(key: key);

  @override
  State<PrinterSettingsWidget> createState() => _PrinterSettingsWidgetState();
}

class _PrinterSettingsWidgetState extends State<PrinterSettingsWidget> {
  final PrinterService _printerService = PrinterService();
  List<BluetoothDevice> _devices = [];
  BluetoothDevice? _selectedPrinter;
  bool _isScanning = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSelectedPrinter();
  }

  Future<void> _loadSelectedPrinter() async {
    setState(() => _isLoading = true);
    await _printerService.init();
    setState(() {
      _selectedPrinter = _printerService.selectedPrinter;
      _isLoading = false;
    });
  }

  Future<void> _scanForPrinters() async {
    setState(() => _isScanning = true);
    try {
      final devices = await _printerService.scanBluetoothPrinters();
      setState(() {
        _devices = devices;
        _isScanning = false;
      });
    } catch (e) {
      setState(() => _isScanning = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error scanning: $e')),
        );
      }
    }
  }

  Future<void> _connectToPrinter(BluetoothDevice printer) async {
    setState(() => _isLoading = true);
    try {
      final success = await _printerService.connectToBluetoothPrinter(printer);
      if (success) {
        setState(() {
          _selectedPrinter = printer;
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Connected to ${printer.platformName}')),
          );
        }
      } else {
        setState(() => _isLoading = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to connect to printer')),
          );
        }
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Connection error: $e')),
        );
      }
    }
  }

  Future<void> _testPrint() async {
    setState(() => _isLoading = true);
    try {
      final success = await _printerService.printTestReceipt();
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'Test print sent successfully!' : 'Test print failed'),
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Print error: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Printer Settings',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        // Current printer status
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Current Printer:', style: TextStyle(fontWeight: FontWeight.w500)),
                const SizedBox(height: 8),
                Text(
                  _selectedPrinter != null
                      ? _selectedPrinter!.platformName.isNotEmpty 
                          ? _selectedPrinter!.platformName 
                          : 'Unknown Printer'
                      : 'No printer selected',
                  style: TextStyle(
                    color: _selectedPrinter != null ? Colors.green : Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text('Status: '),
                    Text(
                      _printerService.isConnected ? 'Connected' : 'Disconnected',
                      style: TextStyle(
                        color: _printerService.isConnected ? Colors.green : Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Action buttons
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: _isScanning || _isLoading ? null : _scanForPrinters,
              icon: _isScanning 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.search),
              label: Text(_isScanning ? 'Scanning...' : 'Scan for Printers'),
            ),
            const SizedBox(width: 8),
            if (_selectedPrinter != null)
              ElevatedButton.icon(
                onPressed: _isLoading ? null : _testPrint,
                icon: _isLoading 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.print),
                label: const Text('Test Print'),
              ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Available printers list
        if (_devices.isNotEmpty) ...[
          const Text('Available Printers:', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          ...(_devices.map((printer) => Card(
            child: ListTile(
              title: Text(printer.platformName.isNotEmpty 
                  ? printer.platformName 
                  : 'Unknown Printer'),
              subtitle: Text(printer.remoteId.str),
              trailing: _selectedPrinter?.remoteId.str == printer.remoteId.str
                  ? const Icon(Icons.check_circle, color: Colors.green)
                  : const Icon(Icons.radio_button_unchecked),
              onTap: _isLoading ? null : () => _connectToPrinter(printer),
            ),
          ))),
        ] else if (_devices.isEmpty && !_isScanning) ...[
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'No printers found. Make sure your thermal printer is turned on and in pairing mode, then tap "Scan for Printers".',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ),
        ],
        
        const SizedBox(height: 16),
        
        // Help text
        const Card(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Setup Instructions:', style: TextStyle(fontWeight: FontWeight.w500)),
                SizedBox(height: 8),
                Text('1. Turn on your thermal printer'),
                Text('2. Make sure Bluetooth is enabled on this device'),
                Text('3. Tap "Scan for Printers" to find available devices'),
                Text('4. Select your printer from the list'),
                Text('5. Use "Test Print" to verify the connection'),
              ],
            ),
          ),
        ),
      ],
    );
  }
}