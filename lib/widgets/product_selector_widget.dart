import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/pos_models.dart';

class ProductSelectorWidget extends StatelessWidget {
  final List<Product> products;
  final List<Product> filteredProducts;
  final Function(Product, double) onAddToCart;
  final Function(String) onSearchChanged;
  final TextEditingController searchController;
  final TextEditingController weightController;
  final String searchFilterType;
  final Function(String) onFilterTypeChanged;

  const ProductSelectorWidget({
    Key? key,
    required this.products,
    required this.filteredProducts,
    required this.onAddToCart,
    required this.onSearchChanged,
    required this.searchController,
    required this.weightController,
    required this.searchFilterType,
    required this.onFilterTypeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search and filter section
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: searchFilterType == 'Name' 
                        ? 'Search products by name...' 
                        : 'Search by barcode...',
                    prefixIcon: const Icon(Icons.search),
                    border: const OutlineInputBorder(),
                    isDense: true,
                  ),
                  onChanged: onSearchChanged,
                ),
              ),
              const SizedBox(width: 8),
              DropdownButton<String>(
                value: searchFilterType,
                items: const [
                  DropdownMenuItem(value: 'Name', child: Text('Name')),
                  DropdownMenuItem(value: 'Barcode', child: Text('Barcode')),
                ],
                onChanged: (value) => onFilterTypeChanged(value!),
              ),
            ],
          ),
        ),
        
        // Products list
        Expanded(
          child: filteredProducts.isEmpty
              ? const Center(
                  child: Text(
                    'No products found',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  itemCount: filteredProducts.length,
                  itemBuilder: (context, index) {
                    final product = filteredProducts[index];
                    return ProductListTile(
                      product: product,
                      onAddToCart: onAddToCart,
                      weightController: weightController,
                    );
                  },
                ),
        ),
      ],
    );
  }
}

class ProductListTile extends StatefulWidget {
  final Product product;
  final Function(Product, double) onAddToCart;
  final TextEditingController weightController;

  const ProductListTile({
    Key? key,
    required this.product,
    required this.onAddToCart,
    required this.weightController,
  }) : super(key: key);

  @override
  State<ProductListTile> createState() => _ProductListTileState();
}

class _ProductListTileState extends State<ProductListTile> {
  final TextEditingController _localWeightController = TextEditingController(text: '1');

  @override
  void dispose() {
    _localWeightController.dispose();
    super.dispose();
  }

  void _addToCart() {
    final weight = double.tryParse(_localWeightController.text) ?? 1.0;
    if (weight > 0) {
      widget.onAddToCart(widget.product, weight);
      _localWeightController.text = '1'; // Reset to default
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid quantity')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final product = widget.product;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        title: Text(
          product.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(product.description),
            Text(
              'Price: \$${product.price.toStringAsFixed(2)}/kg',
              style: const TextStyle(
                color: Colors.green,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              'Stock: ${product.quantity.toStringAsFixed(1)} kg',
              style: TextStyle(
                color: product.quantity > 0 ? Colors.blue : Colors.red,
              ),
            ),
            if (product.barcode != null && product.barcode!.isNotEmpty)
              Text(
                'Barcode: ${product.barcode}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
          ],
        ),
        trailing: SizedBox(
          width: 120,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 60,
                child: TextField(
                  controller: _localWeightController,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                  ],
                  decoration: const InputDecoration(
                    hintText: 'Qty',
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    border: OutlineInputBorder(),
                  ),
                  style: const TextStyle(fontSize: 12),
                ),
              ),
              const SizedBox(width: 4),
              IconButton(
                icon: const Icon(Icons.add_shopping_cart, color: Colors.blue),
                onPressed: product.quantity > 0 ? _addToCart : null,
                tooltip: product.quantity > 0 ? 'Add to cart' : 'Out of stock',
              ),
            ],
          ),
        ),
        enabled: product.quantity > 0,
      ),
    );
  }
}