import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:sunmi_printer_plus/core/enums/enums.dart';
import 'package:sunmi_printer_plus/core/sunmi/sunmi_printer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import '../models/pos_models.dart';
import 'database_service.dart';

enum PrinterType { bluetooth, sunmi, none }

class PrinterService {
  static final PrinterService _instance = PrinterService._internal();
  factory PrinterService() => _instance;
  PrinterService._internal();

  // Bluetooth printer manager
  List<BluetoothDevice> _devices = [];
  BluetoothDevice? _selectedPrinter;
  BluetoothCharacteristic? _writeCharacteristic;
  PrinterType _activePrinterType = PrinterType.none;
  bool _isConnected = false;
  bool _isSunmiAvailable = false;
  bool _isScanning = false;

  // Getters
  List<BluetoothDevice> get devices => _devices;
  BluetoothDevice? get selectedPrinter => _selectedPrinter;
  PrinterType get activePrinterType => _activePrinterType;
  bool get isConnected => _isConnected;
  bool get isScanning => _isScanning;

  // Initialize the printer service
  Future<void> init() async {
    if (Platform.isAndroid) {
      // Check if Sunmi printer is available
      try {
        final bool? result = await SunmiPrinter.bindingPrinter();
        _isSunmiAvailable = result ?? false;
        if (_isSunmiAvailable) {
          _activePrinterType = PrinterType.sunmi;
          _isConnected = true;
        }
      } catch (e) {
        debugPrint('Sunmi printer not available: $e');
        _isSunmiAvailable = false;
      }
    }

    // Load saved printer settings
    await _loadPrinterSettings();
  }

  // Load saved printer settings from SharedPreferences
  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final printerTypeStr = prefs.getString('printer_type');
    final printerAddress = prefs.getString('printer_address');

    if (printerTypeStr != null) {
      _activePrinterType = PrinterType.values.firstWhere(
        (type) => type.toString() == printerTypeStr,
        orElse: () => PrinterType.none,
      );

      if (_activePrinterType == PrinterType.bluetooth && printerAddress != null) {
        // Create a device reference from saved settings
        _selectedPrinter = BluetoothDevice(
          remoteId: DeviceIdentifier(printerAddress),
        );
      } else if (_activePrinterType == PrinterType.sunmi) {
        _isConnected = _isSunmiAvailable;
      }
    }
  }

  // Save printer settings to SharedPreferences
  Future<void> savePrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('printer_type', _activePrinterType.toString());
    if (_selectedPrinter != null) {
      await prefs.setString('printer_address', _selectedPrinter!.remoteId.str);
      await prefs.setString('printer_name', _selectedPrinter!.platformName);
    }
  }

  // Scan for Bluetooth printers
  Future<List<BluetoothDevice>> scanBluetoothPrinters() async {
    _devices = [];
    _isScanning = true;
    
    try {
      // Check if Bluetooth is available and on
      if (await FlutterBluePlus.isSupported == false) {
        debugPrint('Bluetooth not available');
        _isScanning = false;
        return [];
      }

      final adapterState = await FlutterBluePlus.adapterState.first;
      if (adapterState != BluetoothAdapterState.on) {
        debugPrint('Bluetooth is off');
        _isScanning = false;
        return [];
      }

      // Start scanning
      await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 10),
        withServices: [], // Scan for all devices
      );

      // Listen to scan results
      FlutterBluePlus.scanResults.listen((results) {
        _devices = results
            .map((r) => r.device)
            .where((device) => device.platformName.isNotEmpty)
            .toList();
      });

      // Wait for scan to complete
      await Future.delayed(const Duration(seconds: 10));
      await FlutterBluePlus.stopScan();
      
      _isScanning = false;
      return _devices;
    } catch (e) {
      debugPrint('Error scanning for Bluetooth printers: $e');
      _isScanning = false;
      return [];
    }
  }

  // Connect to a Bluetooth printer
  Future<bool> connectToBluetoothPrinter(BluetoothDevice printer) async {
    try {
      // Disconnect if already connected to another device
      if (_selectedPrinter != null && _isConnected) {
        await _selectedPrinter!.disconnect();
      }

      // Connect to the new printer
      await printer.connect();
      
      // Discover services
      List<BluetoothService> services = await printer.discoverServices();
      
      // Find a writable characteristic (usually for printers)
      BluetoothCharacteristic? writeChar;
      for (BluetoothService service in services) {
        for (BluetoothCharacteristic characteristic in service.characteristics) {
          if (characteristic.properties.write || characteristic.properties.writeWithoutResponse) {
            writeChar = characteristic;
            break;
          }
        }
        if (writeChar != null) break;
      }

      if (writeChar == null) {
        debugPrint('No writable characteristic found for printer');
        await printer.disconnect();
        return false;
      }

      _writeCharacteristic = writeChar;
      _selectedPrinter = printer;
      _activePrinterType = PrinterType.bluetooth;
      _isConnected = true;
      await savePrinterSettings();
      
      return true;
    } catch (e) {
      debugPrint('Error connecting to Bluetooth printer: $e');
      return false;
    }
  }

  // Disconnect from current printer
  Future<void> disconnect() async {
    if (_selectedPrinter != null && _isConnected) {
      try {
        await _selectedPrinter!.disconnect();
        _isConnected = false;
        _writeCharacteristic = null;
      } catch (e) {
        debugPrint('Error disconnecting from printer: $e');
      }
    }
  }

  // Print receipt using the selected printer
  Future<bool> printReceipt({
    required Sale sale,
    required List<SaleItem> items,
    required CompanySettings companySettings,
    required String cashierName,
    required double cashReceived,
    required String paymentMethod,
  }) async {
    try {
      switch (_activePrinterType) {
        case PrinterType.bluetooth:
          return await _printBluetoothReceipt(
            sale: sale,
            items: items,
            companySettings: companySettings,
            cashierName: cashierName,
            cashReceived: cashReceived,
            paymentMethod: paymentMethod,
          );
        case PrinterType.sunmi:
          return await _printSunmiReceipt(
            sale: sale,
            items: items,
            companySettings: companySettings,
            cashierName: cashierName,
            cashReceived: cashReceived,
            paymentMethod: paymentMethod,
          );
        case PrinterType.none:
          debugPrint('No printer selected');
          return false;
      }
    } catch (e) {
      debugPrint('Error printing receipt: $e');
      return false;
    }
  }

  // Generate ESC/POS commands for thermal printers
  List<int> _generateReceiptBytes({
    required Sale sale,
    required List<SaleItem> items,
    required CompanySettings companySettings,
    required String cashierName,
    required double cashReceived,
    required String paymentMethod,
  }) {
    List<int> bytes = [];
    
    // ESC/POS commands
    bytes.addAll([27, 64]); // Initialize printer
    bytes.addAll([27, 97, 1]); // Center alignment
    
    // Company name (bold)
    bytes.addAll([27, 69, 1]); // Bold on
    bytes.addAll(utf8.encode(companySettings.name));
    bytes.addAll([10]); // Line feed
    bytes.addAll([27, 69, 0]); // Bold off
    
    // Company details
    bytes.addAll(utf8.encode(companySettings.address));
    bytes.addAll([10]);
    bytes.addAll(utf8.encode('Tel: ${companySettings.phone}'));
    bytes.addAll([10]);
    bytes.addAll(utf8.encode('Tax No: ${companySettings.taxNumber}'));
    bytes.addAll([10, 10]);
    
    // Left align for receipt details
    bytes.addAll([27, 97, 0]); // Left alignment
    
    // Separator line
    bytes.addAll(utf8.encode('-' * 32));
    bytes.addAll([10]);
    
    // Receipt info
    bytes.addAll(utf8.encode('Receipt #: ${sale.receiptNumber}'));
    bytes.addAll([10]);
    bytes.addAll(utf8.encode('Date: ${DateFormat('yyyy-MM-dd HH:mm').format(sale.dateTime)}'));
    bytes.addAll([10]);
    bytes.addAll(utf8.encode('Cashier: $cashierName'));
    bytes.addAll([10]);
    
    // Separator line
    bytes.addAll(utf8.encode('-' * 32));
    bytes.addAll([10]);
    
    // Items header
    bytes.addAll(utf8.encode('Item            Qty   Price  Total'));
    bytes.addAll([10]);
    bytes.addAll(utf8.encode('-' * 32));
    bytes.addAll([10]);
    
    // Items
    for (final item in items) {
      final name = item.productName.length > 12 
          ? item.productName.substring(0, 12) 
          : item.productName.padRight(12);
      final qty = item.quantity.toString().padLeft(5);
      final price = item.price.toStringAsFixed(2).padLeft(7);
      final total = item.total.toStringAsFixed(2).padLeft(6);
      bytes.addAll(utf8.encode('$name $qty $price $total'));
      bytes.addAll([10]);
    }
    
    // Separator line
    bytes.addAll(utf8.encode('-' * 32));
    bytes.addAll([10]);
    
    // Totals
    bytes.addAll(utf8.encode('Subtotal:               ${sale.subtotal.toStringAsFixed(2)}'));
    bytes.addAll([10]);
    if (sale.vatTotal > 0) {
      bytes.addAll(utf8.encode('Tax:                    ${sale.vatTotal.toStringAsFixed(2)}'));
      bytes.addAll([10]);
    }
    bytes.addAll(utf8.encode('Total:                  ${sale.total.toStringAsFixed(2)}'));
    bytes.addAll([10]);
    bytes.addAll(utf8.encode('Payment Method:         $paymentMethod'));
    bytes.addAll([10]);
    
    if (paymentMethod == 'Cash') {
      bytes.addAll(utf8.encode('Cash Received:          ${cashReceived.toStringAsFixed(2)}'));
      bytes.addAll([10]);
      bytes.addAll(utf8.encode('Change:                 ${(cashReceived - sale.total).toStringAsFixed(2)}'));
      bytes.addAll([10]);
    }
    
    // Separator line
    bytes.addAll(utf8.encode('-' * 32));
    bytes.addAll([10, 10]);
    
    // Footer
    bytes.addAll([27, 97, 1]); // Center alignment
    if (companySettings.receiptFooter.isNotEmpty) {
      bytes.addAll(utf8.encode(companySettings.receiptFooter));
      bytes.addAll([10]);
    }
    bytes.addAll(utf8.encode('Thank you for your business!'));
    bytes.addAll([10, 10, 10]);
    
    // Cut paper
    bytes.addAll([29, 86, 65, 3]); // Partial cut
    
    return bytes;
  }

  // Print receipt using Bluetooth printer
  Future<bool> _printBluetoothReceipt({
    required Sale sale,
    required List<SaleItem> items,
    required CompanySettings companySettings,
    required String cashierName,
    required double cashReceived,
    required String paymentMethod,
  }) async {
    if (_selectedPrinter == null || _writeCharacteristic == null) {
      debugPrint('No printer or characteristic available');
      return false;
    }

    try {
      // Check if still connected
      if (!_isConnected || _selectedPrinter!.isDisconnected) {
        debugPrint('Printer not connected, attempting to reconnect...');
        final success = await connectToBluetoothPrinter(_selectedPrinter!);
        if (!success) {
          debugPrint('Failed to reconnect to printer');
          return false;
        }
      }

      // Generate receipt bytes
      final receiptBytes = _generateReceiptBytes(
        sale: sale,
        items: items,
        companySettings: companySettings,
        cashierName: cashierName,
        cashReceived: cashReceived,
        paymentMethod: paymentMethod,
      );

      // Send data in chunks to avoid buffer overflow
      const chunkSize = 20;
      for (int i = 0; i < receiptBytes.length; i += chunkSize) {
        final end = (i + chunkSize < receiptBytes.length) ? i + chunkSize : receiptBytes.length;
        final chunk = receiptBytes.sublist(i, end);
        
        await _writeCharacteristic!.write(
          chunk, 
          withoutResponse: _writeCharacteristic!.properties.writeWithoutResponse
        );
        
        // Small delay between chunks
        await Future.delayed(const Duration(milliseconds: 50));
      }

      return true;
    } catch (e) {
      debugPrint('Error printing Bluetooth receipt: $e');
      return false;
    }
  }

  // Print receipt using Sunmi printer
  Future<bool> _printSunmiReceipt({
    required Sale sale,
    required List<SaleItem> items,
    required CompanySettings companySettings,
    required String cashierName,
    required double cashReceived,
    required String paymentMethod,
  }) async {
    if (!_isSunmiAvailable) return false;

    try {
      await SunmiPrinter.bindingPrinter();
      
      // Company info (centered and bold)
      await SunmiPrinter.setAlignment(SunmiPrintAlign.CENTER);
      await SunmiPrinter.bold();
      await SunmiPrinter.printText(companySettings.name);
      await SunmiPrinter.resetBold();
      await SunmiPrinter.line();
      
      // Company details
      await SunmiPrinter.printText(companySettings.address);
      await SunmiPrinter.line();
      await SunmiPrinter.printText('Tel: ${companySettings.phone}');
      await SunmiPrinter.line();
      await SunmiPrinter.printText('Tax No: ${companySettings.taxNumber}');
      await SunmiPrinter.line();
      
      // Separator
      await SunmiPrinter.setAlignment(SunmiPrintAlign.LEFT);
      await SunmiPrinter.printText('-' * 32);
      await SunmiPrinter.line();
      
      // Receipt info
      await SunmiPrinter.printText('Receipt #: ${sale.receiptNumber}');
      await SunmiPrinter.line();
      await SunmiPrinter.printText('Date: ${DateFormat('yyyy-MM-dd HH:mm').format(sale.dateTime)}');
      await SunmiPrinter.line();
      await SunmiPrinter.printText('Cashier: $cashierName');
      await SunmiPrinter.line();
      await SunmiPrinter.printText('-' * 32);
      await SunmiPrinter.line();
      
      // Items header
      await SunmiPrinter.printText('Item            Qty   Price  Total');
      await SunmiPrinter.line();
      await SunmiPrinter.printText('-' * 32);
      await SunmiPrinter.line();
      
      // Items
      for (final item in items) {
        final name = item.productName.length > 12 
            ? item.productName.substring(0, 12) 
            : item.productName.padRight(12);
        final qty = item.quantity.toString().padLeft(5);
        final price = item.price.toStringAsFixed(2).padLeft(7);
        final total = item.total.toStringAsFixed(2).padLeft(6);
        await SunmiPrinter.printText('$name $qty $price $total');
        await SunmiPrinter.line();
      }
      
      await SunmiPrinter.printText('-' * 32);
      await SunmiPrinter.line();
      
      // Totals
      await SunmiPrinter.printText('Subtotal:               ${sale.subtotal.toStringAsFixed(2)}');
      await SunmiPrinter.line();
      if (sale.vatTotal > 0) {
        await SunmiPrinter.printText('Tax:                    ${sale.vatTotal.toStringAsFixed(2)}');
        await SunmiPrinter.line();
      }
      await SunmiPrinter.printText('Total:                  ${sale.total.toStringAsFixed(2)}');
      await SunmiPrinter.line();
      await SunmiPrinter.printText('Payment Method:         $paymentMethod');
      await SunmiPrinter.line();
      
      if (paymentMethod == 'Cash') {
        await SunmiPrinter.printText('Cash Received:          ${cashReceived.toStringAsFixed(2)}');
        await SunmiPrinter.line();
        await SunmiPrinter.printText('Change:                 ${(cashReceived - sale.total).toStringAsFixed(2)}');
        await SunmiPrinter.line();
      }
      
      await SunmiPrinter.printText('-' * 32);
      await SunmiPrinter.line();
      await SunmiPrinter.line();
      
      // Footer
      await SunmiPrinter.setAlignment(SunmiPrintAlign.CENTER);
      if (companySettings.receiptFooter.isNotEmpty) {
        await SunmiPrinter.printText(companySettings.receiptFooter);
        await SunmiPrinter.line();
      }
      await SunmiPrinter.printText('Thank you for your business!');
      await SunmiPrinter.line();
      await SunmiPrinter.line();
      await SunmiPrinter.line();
      
      // Cut paper
      await SunmiPrinter.cut();
      
      return true;
    } catch (e) {
      debugPrint('Error printing Sunmi receipt: $e');
      return false;
    }
  }

  // Test print a sample receipt
  Future<bool> printTestReceipt() async {
    try {
      final companySettings = await DatabaseService.instance.getCompanySettings() ?? 
          CompanySettings(
            name: 'Test Company',
            address: 'Test Address',
            phone: '*********',
            taxNumber: '*********',
            email: '<EMAIL>',
            receiptFooter: 'Test Footer',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

      final sale = Sale(
        id: 0,
        receiptNumber: 'TEST-${DateTime.now().millisecondsSinceEpoch}',
        dateTime: DateTime.now(),
        cashierName: 'Test Cashier',
        subtotal: 100.0,
        vatTotal: 10.0,
        total: 110.0,
        cashReceived: 120.0,
        items: [],
        paymentMethod: 'Cash',
        change: 10.0,
        createdAt: DateTime.now(),
      );

      final items = [
        SaleItem(
          id: 0,
          saleId: 0,
          productId: 1,
          productName: 'Test Product 1',
          quantity: 2,
          price: 25.0,
          total: 50.0,
          cost: 20.0,
          taxRate: 0.1,
          profit: 30.0,
          createdAt: DateTime.now(),
        ),
        SaleItem(
          id: 0,
          saleId: 0,
          productId: 2,
          productName: 'Test Product 2',
          quantity: 1,
          price: 50.0,
          total: 50.0,
          cost: 40.0,
          taxRate: 0.1,
          profit: 10.0,
          createdAt: DateTime.now(),
        ),
      ];

      return await printReceipt(
        sale: sale,
        items: items,
        companySettings: companySettings,
        cashierName: 'Test Cashier',
        cashReceived: 120.0,
        paymentMethod: 'Cash',
      );
    } catch (e) {
      debugPrint('Error printing test receipt: $e');
      return false;
    }
  }

  // Get saved printers list
  Future<List<Map<String, String>>> getSavedPrinters() async {
    final prefs = await SharedPreferences.getInstance();
    final savedPrinters = prefs.getStringList('saved_printers') ?? [];
    return savedPrinters.map((printerJson) {
      final Map<String, dynamic> data = jsonDecode(printerJson);
      return {
        'name': data['name'] as String,
        'address': data['address'] as String,
        'type': data['type'] as String,
      };
    }).toList();
  }

  // Save printer to list
  Future<void> savePrinterToList(String name, String address, String type) async {
    final prefs = await SharedPreferences.getInstance();
    final savedPrinters = await getSavedPrinters();
    
    // Check if printer already exists
    final exists = savedPrinters.any((p) => p['address'] == address);
    if (!exists) {
      savedPrinters.add({
        'name': name,
        'address': address,
        'type': type,
      });
      
      final jsonList = savedPrinters.map((printer) => jsonEncode(printer)).toList();
      await prefs.setStringList('saved_printers', jsonList);
    }
  }

  // Remove printer from list
  Future<void> removePrinterFromList(String address) async {
    final prefs = await SharedPreferences.getInstance();
    final savedPrinters = await getSavedPrinters();
    savedPrinters.removeWhere((p) => p['address'] == address);
    
    final jsonList = savedPrinters.map((printer) => jsonEncode(printer)).toList();
    await prefs.setStringList('saved_printers', jsonList);
  }
}