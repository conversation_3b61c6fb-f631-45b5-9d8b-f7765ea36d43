import 'dart:ffi';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:ffi/ffi.dart';
import 'package:flutter/services.dart';
import 'package:sunmi_printer_plus/core/enums/enums.dart';
import '../services/bluetooth_printer_service.dart';
import '../services/bluetooth_print_utils.dart';
import 'package:sunmi_printer_plus/core/sunmi/sunmi_printer.dart';
import 'package:sunmi_printer_plus/sunmi_printer_plus.dart';
import 'package:win32/win32.dart';
import 'package:allanwebapp_mobile_pos/screens/login_screen.dart';
import '../models/pos_models.dart';
import '../services/database_service.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CashierScreen extends StatefulWidget {
  final String cashierName;
  final int userId;

  const CashierScreen({required this.cashierName, required this.userId, Key? key}) : super(key: key);

  @override
  _CashierScreenState createState() => _CashierScreenState();
}

class _CashierScreenState extends State<CashierScreen> {
  final DatabaseService _db = DatabaseService.instance;
  final TextEditingController _cashReceivedController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();

  List<Product> _products = [];
  List<Product> _filteredProducts = [];
  Map<int, double> _cart = {}; // productId -> weight/quantity
  CompanySettings? _companySettings;
  bool _isLoading = false;
  bool _isDarkMode = false;
  String _paymentMethod = 'Cash';

  // Add filter type for search
  String _searchFilterType = 'Name'; // or 'Barcode'

  // Add today's total sales for the cashier
  double _todayTotalSales = 0.0;

  final Color _lightBluePrimary = const Color(0xFF64B5F6);
  final Color _lightBlueAccent = const Color(0xFF1E88E5);
  final Color _lightBackground = const Color(0xFFE3F2FD);
  final Color _darkPrimary = const Color(0xFF0D47A1);
  final Color _darkAccent = const Color(0xFF1976D2);
  final Color _darkBackground = const Color(0xFF121212);

  @override
  void initState() {
    super.initState();
    _loadProducts();
    _loadCompanySettings();
    _searchController.addListener(_filterProducts);
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _filterProducts() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredProducts = _products.where((product) {
        if (_searchFilterType == 'Name') {
          return product.name.toLowerCase().contains(query);
        } else if (_searchFilterType == 'Barcode') {
          return product.barcode != null && product.barcode!.toLowerCase().contains(query);
        }
        return false;
      }).toList();
    });
  }

  Future<void> _loadProducts() async {
    setState(() => _isLoading = true);
    try {
      final products = await _db.getAllProducts();
      setState(() {
        _products = products;
        _filteredProducts = products;
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadCompanySettings() async {
    final settings = await _db.getCompanySettings();
    setState(() => _companySettings = settings);
  }

  Future<void> _fetchTodayTotalSales() async {
    final now = DateTime.now();
    final formattedDate = '${now.year}-${_twoDigits(now.month)}-${_twoDigits(now.day)}';
    final cashSales = await _db.getTotalSalesByCashierAndPaymentMethodAndDate(
      widget.cashierName,
      'Cash',
      formattedDate,
    );
    final bankSales = await _db.getTotalSalesByCashierAndPaymentMethodAndDate(
      widget.cashierName,
      'Bank Transfer',
      formattedDate,
    );
    setState(() {
      _todayTotalSales = cashSales + bankSales;
    });
  }

  void _showProductDialog(Product product) {
    // Set the controller to the current cart quantity if present, else clear
    final currentQty = _cart[product.id!] ?? 0;
    _weightController.text = currentQty > 0 ? currentQty.toString() : '';

    showDialog(
      context: context,
      builder: (context) {
        double totalCost = 0;
        double taxAmount = 0;
        double totalWithTax = 0;

        return StatefulBuilder(
          builder: (context, setDialogState) {
            void calculateTotal() {
              final weight = double.tryParse(_weightController.text) ?? 0;
              totalCost = product.price * weight;
              taxAmount = (product.isTaxed && product.taxRate > 0) ? totalCost * (product.taxRate / 100) : 0;
              totalWithTax = totalCost + taxAmount;
              setDialogState(() {});
            }

            return AlertDialog(
              title: Row(
                children: [
                  Text(product.name),
                  if (product.isTaxed && product.taxRate > 0)
                    Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.orange[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text('Taxed (${product.taxRate}%)', style: const TextStyle(fontSize: 11, color: Colors.orange)),
                      ),
                    ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: _weightController,
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    decoration: const InputDecoration(
                      labelText: 'Enter Quantity (kg/units)',
                      border: OutlineInputBorder(),
                    ),
                    autofocus: true,
                    onChanged: (value) => calculateTotal(),
                  ),
                  const SizedBox(height: 12),
                  Text('\$${product.price.toStringAsFixed(2)}', style: const TextStyle(fontSize: 13, color: Colors.green)),
                  if (product.isTaxed && product.taxRate > 0)
                    Text('Tax: ${product.taxRate}%'),
                  const SizedBox(height: 8),
                  if (product.isTaxed && product.taxRate > 0)
                    Text('Tax Amount: \$${taxAmount.toStringAsFixed(2)}', style: const TextStyle(fontSize: 13)),
                  Text('Total: \$${(product.isTaxed && product.taxRate > 0 ? totalWithTax : totalCost).toStringAsFixed(2)}', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    final weight = double.tryParse(_weightController.text) ?? 0;
                    if (weight < 0) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Please enter a valid quantity')),
                      );
                      return;
                    }
                    if (weight > product.quantity) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Not enough stock available')),
                      );
                      return;
                    }
                    setState(() {
                      if (weight <= 0) {
                        _cart.remove(product.id!);
                      } else {
                        _cart[product.id!] = weight;
                      }
                    });
                    Navigator.pop(context);
                  },
                  child: const Text('Apply'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _addToCartWithWeight(Product product, double weight) {
    setState(() {
      _cart[product.id!] = weight;
    });
  }

  void _addToCart(Product product) {
    if (product.quantity <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Product is out of stock')),
      );
      return;
    }
    setState(() {
      final currentQty = _cart[product.id!] ?? 0;
      if (currentQty + 1 > product.quantity) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Not enough stock available')),
        );
        return;
      }
      _cart[product.id!] = currentQty + 1;
    });
  }

  void _removeFromCart(Product product) {
    setState(() {
      _cart.remove(product.id);
    });
  }

  double _calculateSubtotal() {
    double subtotal = 0;
    for (var entry in _cart.entries) {
      final product = _products.firstWhere((p) => p.id == entry.key);
      subtotal += product.price * entry.value;
    }
    return subtotal;
  }

  double _calculateVAT() {
    double vat = 0;
    for (var entry in _cart.entries) {
      final product = _products.firstWhere((p) => p.id == entry.key);
      if (product.isTaxed && product.taxRate > 0) {
        vat += (product.price * entry.value) * (product.taxRate / 100);
      }
    }
    return vat;
  }

  double _calculateTotal() {
    double total = 0;
    for (var entry in _cart.entries) {
      final product = _products.firstWhere((p) => p.id == entry.key);
      if (product.isTaxed && product.taxRate > 0) {
        total += (product.price * entry.value) * (1 + product.taxRate / 100);
      } else {
        total += product.price * entry.value;
      }
    }
    return total;
  }

  Future<void> _processSale() async {
    if (widget.cashierName.isEmpty || widget.userId == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Invalid cashier information. Please log in again.')),
      );
      return;
    }

    print('DEBUG: cashierName=${widget.cashierName}, userId=${widget.userId}');
    if (_cart.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Cart is empty')),
      );
      return;
    }

    final subtotal = _calculateSubtotal();
    final vat = _calculateVAT();
    final total = _calculateTotal();

    final method = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Payment Method'),
        content: DropdownButton<String>(
          isExpanded: true,
          value: _paymentMethod,
          items: ['Cash', 'Bank Transfer'].map((method) {
            return DropdownMenuItem(
              value: method,
              child: Text(method),
            );
          }).toList(),
          onChanged: (value) {
            Navigator.pop(context, value);
          },
        ),
      ),
    );

    if (method == null) return;
    _paymentMethod = method;

    double cashReceived = 0;
    double change = 0;

    if (_paymentMethod == 'Cash') {
      final cashInput = await showCashInputDialog(context, _cashReceivedController);
      if (cashInput == null) return;
      cashReceived = double.tryParse(cashInput) ?? 0;
      change = cashReceived - total;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Sale'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Payment Method: $_paymentMethod'),
            Text('Total: \$${total.toStringAsFixed(2)}'),
            if (_paymentMethod == 'Cash') ...[
              Text('Cash Received: \$${cashReceived.toStringAsFixed(2)}'),
              Text('Change: \$${change.toStringAsFixed(2)}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);
    try {
      final now = DateTime.now();
      final sale = Sale(
        receiptNumber: await Sale.generateReceiptNumber(_db),
        dateTime: now,
        cashierName: widget.cashierName,
        cashierId: widget.userId,
        subtotal: subtotal,
        vatTotal: vat,
        total: total,
        cashReceived: cashReceived,
        change: change,
        paymentMethod: _paymentMethod,
        createdAt: now,
        items: _cart.entries.map((entry) {
          final product = _products.firstWhere((p) => p.id == entry.key);
          final quantity = entry.value;
          final cost = product.cost;
          final taxRate = product.taxRate;
          final isTaxed = product.isTaxed && product.taxRate > 0;
          final taxAmount = isTaxed ? (product.price * quantity) * (product.taxRate / (100 + product.taxRate)) : 0;
          final baseTotal = (product.price * quantity) - taxAmount;
          final totalWithTax = product.price * quantity;
          final profit = (product.price - cost) * quantity;
          return SaleItem(
            saleId: 0,
            productId: product.id!,
            productName: product.name,
            quantity: quantity,
            price: product.price,
            total: totalWithTax,
            cost: cost,
            taxRate: taxRate,
            profit: profit,
            createdAt: now,
          );
        }).toList(),
      );

      await _db.insertSale(sale);
      await _updateProductQuantities();
      await printReceiptDirect(sale, _companySettings);

      setState(() {
        _cart.clear();
        _cashReceivedController.clear();
      });
      // Update today's total sales after a sale
      await _fetchTodayTotalSales();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Sale completed successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error processing sale: $e')),
      
      );
      print('Error Processing Sale:$e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateProductQuantities() async {
    for (var entry in _cart.entries) {
      final product = _products.firstWhere((p) => p.id == entry.key);
      final updatedProduct = product.copyWith(
        quantity: product.quantity - entry.value,
      );
      await _db.updateProduct(updatedProduct);
    }
    await _loadProducts();
  }

  Future<void> printReceiptDirect(Sale sale, CompanySettings? settings) async {
    final receiptText = _generatePlainReceipt(sale, settings);
    try {
      if (Platform.isAndroid) {
        final profile = await CapabilityProfile.load();
        final generator = Generator(PaperSize.mm80, profile);
        final List<int> bytes = generator.text(receiptText);
        await printReceiptWithDefaultPrinter(bytes, context);
        return;
      } else if (Platform.isWindows) {
        final prefs = await SharedPreferences.getInstance();
        final printerName = prefs.getString('selected_printer') ?? 'POS-80C';
        await _directPrintToPOS80WithName(receiptText, printerName);
        return;
      } else {
        debugPrint('Direct printing is only supported on Android (esc_pos_bluetooth) and Windows for now.');
      }
    } catch (e) {
      debugPrint('Printing failed: $e');
    }
  }

  String _generatePlainReceipt(Sale sale, CompanySettings? settings) {
    final receipt = StringBuffer();
    final companyName = (settings?.name.toUpperCase() ?? 'POS SYSTEM');
    final totalWidth = 32;
    final centeredCompany = companyName.padLeft(((totalWidth + companyName.length) ~/ 2)).padRight(totalWidth);
    receipt.writeln(centeredCompany);
    receipt.writeln('');

    if (settings?.address != null) {
      receipt.writeln(settings!.address);
    }
    if (settings?.phone != null) {
      receipt.writeln('Tel: ${settings!.phone}');
      receipt.writeln('');
    }

    receipt.writeln('Receipt#: ${sale.receiptNumber}');
    receipt.writeln('Date: ${_formatDateTime(sale.dateTime)}');
    receipt.writeln('Cashier: ${sale.cashierName}');
    receipt.writeln('Payment: ${sale.paymentMethod}');
    receipt.writeln('');

    // Adjusted column widths for small receipt (Sunmi V2s)
    // Name: 9, Qty: 4, Price: 6, Tax: 5, Total: 7 (total 31)
    receipt.writeln('ITEM      QTY  PRICE TAX  TOTAL');
    receipt.writeln('-' * 31);

    for (var item in sale.items) {
      final product = _products.firstWhere((p) => p.id == item.productId, orElse: () => Product(
        id: item.productId,
        name: item.productName,
        description: '',
        price: item.price,
        quantity: item.quantity,
        isTaxed: false,
        batchNumber: '',
        cost: 0.0,
        taxRate: 0.0,
      ));
      final isTaxed = product.isTaxed && product.taxRate > 0;
      final displayPrice = isTaxed
          ? product.price * (1 + product.taxRate / 100)
          : product.price;
      // Allow up to 9 chars for name, 4 for qty, 6 for price, 5 for tax, 7 for total
      final name = item.productName.padRight(9).substring(0, 9);
      final qty = item.quantity.toStringAsFixed(2).padLeft(4);
      final price = displayPrice.toStringAsFixed(2).padLeft(6);
      final tax = isTaxed ? (product.price * item.quantity * (product.taxRate / 100)).toStringAsFixed(2).padLeft(5) : ''.padLeft(5);
      final total = item.total.toStringAsFixed(2).padLeft(7);
      receipt.writeln('$name$qty$price$tax$total');
    }

    receipt.writeln('-' * 31);

    receipt.writeln('Subtotal:'.padRight(15) + sale.subtotal.toStringAsFixed(2));
    receipt.writeln('VAT:'.padRight(15) + sale.vatTotal.toStringAsFixed(2));
    receipt.writeln('Total:'.padRight(15) + sale.total.toStringAsFixed(2));

    if (sale.paymentMethod == 'Cash') {
      receipt.writeln('Cash Recv:'.padRight(15) + sale.cashReceived.toStringAsFixed(2));
      receipt.writeln('Change:'.padRight(15) + sale.change.toStringAsFixed(2));
      receipt.writeln('');
    }

    receipt.writeln('');
    receipt.writeln('THANK YOU FOR SHOPPING');
    receipt.writeln('PLEASE VISIT AGAIN');
    receipt.writeln('Powered by AllanWebApp-Cybrox');
    receipt.writeln('=' * 31);
    receipt.writeln('');

    for (int i = 0; i < 6; i++) {
      receipt.writeln('');
    }

    return receipt.toString();
  }

  Future<void> _directPrintToPOS80(String receiptText) async {
    final prefs = await SharedPreferences.getInstance();
    final printerName = prefs.getString('selected_printer') ?? 'POS-80C';

    final hPrinter = calloc<HANDLE>();
    final docInfo = calloc<DOC_INFO_1>()
      ..ref.pDocName = 'POS Receipt'.toNativeUtf16()
      ..ref.pOutputFile = nullptr
      ..ref.pDatatype = 'RAW'.toNativeUtf16();

    final printerOpen = OpenPrinter(printerName.toNativeUtf16(), hPrinter, nullptr);
    if (printerOpen == 0) {
      debugPrint('Failed to open printer $printerName: \\${GetLastError()}');
      calloc.free(hPrinter);
      calloc.free(docInfo);
      return;
    }

    if (StartDocPrinter(hPrinter.value, 1, docInfo.cast()) == 0) {
      debugPrint('Failed to start document: \\${GetLastError()}');
      ClosePrinter(hPrinter.value);
      calloc.free(hPrinter);
      calloc.free(docInfo);
      return;
    }

    StartPagePrinter(hPrinter.value);

    final receiptBytes = Uint8List.fromList(receiptText.codeUnits);
    final pBytes = calloc<Uint8>(receiptBytes.length);
    pBytes.asTypedList(receiptBytes.length).setAll(0, receiptBytes);

    final bytesWritten = calloc<DWORD>();
    WritePrinter(hPrinter.value, pBytes.cast(), receiptBytes.length, bytesWritten);

    EndPagePrinter(hPrinter.value);
    EndDocPrinter(hPrinter.value);
    ClosePrinter(hPrinter.value);

    calloc.free(pBytes);
    calloc.free(bytesWritten);
    calloc.free(hPrinter);
    calloc.free(docInfo);
  }

  Future<String?> showCashInputDialog(BuildContext context, TextEditingController controller) async {
    bool isWindows = Platform.isWindows || Platform.isLinux || Platform.isMacOS;

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Enter Cash Received'),
        content: isWindows
            ? Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildNumericPad(context, controller),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextField(
                          controller: controller,
                          keyboardType: TextInputType.none,
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                          decoration: const InputDecoration(
                            labelText: 'Cash Received',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildConfirmButtons(context, controller),
                      ],
                    ),
                  ),
                ],
              )
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: controller,
                    keyboardType: TextInputType.none,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                    decoration: const InputDecoration(
                      labelText: 'Cash Received',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildNumericPad(context, controller),
                  const SizedBox(height: 12),
                  _buildConfirmButtons(context, controller),
                ],
              ),
      ),
    );
  }

  Widget _buildNumericPad(BuildContext context, TextEditingController controller) {
    return SizedBox(
      width: 180,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 6,
          mainAxisSpacing: 6,
          childAspectRatio: 1.2,
        ),
        itemCount: 12,
        itemBuilder: (context, index) {
          String label;
          if (index < 9) {
            label = '${index + 1}';
          } else if (index == 9) {
            label = 'C';
          } else if (index == 10) {
            label = '0';
          } else {
            label = '⌫';
          }

          return ElevatedButton(
            onPressed: () {
              if (label == 'C') {
                controller.clear();
              } else if (label == '⌫') {
                if (controller.text.isNotEmpty) {
                  controller.text = controller.text.substring(0, controller.text.length - 1);
                }
              } else {
                controller.text += label;
              }
            },
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.all(12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(label, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          );
        },
      ),
    );
  }

  Widget _buildConfirmButtons(BuildContext context, TextEditingController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextButton(
          onPressed: () => Navigator.pop(context, null),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context, controller.text),
          child: const Text('Confirm'),
        ),
      ],
    );
  }

  Widget _buildCartItem(MapEntry<int, double> entry) {
    final product = _products.firstWhere((p) => p.id == entry.key);
    final total = product.price * entry.value;
    
    return ListTile(
      title: Text(product.name),
      subtitle: Text('${entry.value.toStringAsFixed(2)} kg/units @ \$${product.price.toStringAsFixed(2)}'),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('\$${total.toStringAsFixed(2)}'),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showProductDialog(product),
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _removeFromCart(product),
          ),
        ],
      ),
    );
  }

  Widget _buildCartList() {
    return ListView.builder(
      itemCount: _cart.length,
      itemBuilder: (context, index) {
        final entry = _cart.entries.elementAt(index);
        return _buildCartItem(entry);
      },
    );
  }

  String _formatDateTime(DateTime dt) {
    return '${dt.year}-${_twoDigits(dt.month)}-${_twoDigits(dt.day)} ${_twoDigits(dt.hour)}:${_twoDigits(dt.minute)}';
  }

  String _twoDigits(int n) => n.toString().padLeft(2, '0');

  void _toggleTheme() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
  }

  Future<void> _logout() async {
    final now = DateTime.now();
    final formattedDate = '${now.year}-${_twoDigits(now.month)}-${_twoDigits(now.day)}';

    final cashSales = await _db.getTotalSalesByCashierAndPaymentMethodAndDate(
      widget.cashierName,
      'Cash',
      formattedDate,
    );
    final bankSales = await _db.getTotalSalesByCashierAndPaymentMethodAndDate(
      widget.cashierName,
      'Bank Transfer',
      formattedDate,
    );
    final totalSales = cashSales + bankSales;

    final productSummary = await _db.getProductSalesSummaryForCashierOnDate(
      widget.cashierName,
      formattedDate,
    );

    if (!mounted) return;

    final formattedTime = '${_twoDigits(now.hour)}:${_twoDigits(now.minute)}:${_twoDigits(now.second)}';

    // Generate Z-file summary
    final zFileText = _generateZFileSummary(
      formattedDate,
      formattedTime,
      totalSales,
      cashSales,
      bankSales,
      productSummary,
    );

    try {
      // Print Z-file summary
      await _printZFileSummary(zFileText);

      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('End of Shift Summary'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Cashier: ${widget.cashierName}'),
              Text('Date: $formattedDate'),
              Text('Time: $formattedTime'),
              const SizedBox(height: 8),
              Text('Total Sales: \$${totalSales.toStringAsFixed(2)}'),
              Text('Cash at Hand: \$${cashSales.toStringAsFixed(2)}'),
              Text('Bank Transfers: \$${bankSales.toStringAsFixed(2)}'),
              const SizedBox(height: 8),
              const Text('Z-File Summary printed successfully', 
                style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.pushReplacement(context, LoginScreen.route);
              },
              child: const Text('OK'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Z-File printing failed: $e')),
        );
      }
    }
  }

  String _generateSalesSummaryReceipt(
    String formattedDate,
    String formattedTime,
    double totalSales,
    double cashSales,
    double bankSales,
    List<Map<String, dynamic>> productSummary,
  ) {
    final receipt = StringBuffer();

    receipt.writeln('--- End of Shift Report ---');
    receipt.writeln('Date: $formattedDate');
    receipt.writeln('Time: $formattedTime');
    receipt.writeln('Cashier: ${widget.cashierName}');
    receipt.writeln('-' * 32);
    receipt.writeln('Total Sales: \$${totalSales.toStringAsFixed(2)}');
    receipt.writeln('Cash at Hand: \$${cashSales.toStringAsFixed(2)}');
    receipt.writeln('Bank Transfers: \$${bankSales.toStringAsFixed(2)}');
    receipt.writeln('-' * 32);

    if (productSummary.isNotEmpty) {
      receipt.writeln('Items Sold');
      receipt.writeln('-' * 32);

      for (var item in productSummary) {
        final productName = (item['product_name'] as String).padRight(20);
        final quantity = item['total_quantity'].toString().padLeft(5);
        receipt.writeln('$productName $quantity');
      }

      receipt.writeln('-' * 32);
    } else {
      receipt.writeln('No products sold today.');
      receipt.writeln('-' * 32);
    }

    receipt.writeln('Thank you for your hard work!');
    receipt.writeln('-' * 32);

    for (int i = 0; i < 6; i++) {
      receipt.writeln('');
    }

    receipt.writeCharCode(0x1D);
    receipt.writeCharCode(0x56);
    receipt.writeCharCode(0x00);

    return receipt.toString();
  }

  String _generateZFileSummary(
    String formattedDate,
    String formattedTime,
    double totalSales,
    double cashSales,
    double bankSales,
    List<Map<String, dynamic>> productSummary,
  ) {
    // Consolidate products with different prices
    final Map<String, Map<String, dynamic>> consolidatedProducts = {};
    
    for (var item in productSummary) {
      final productName = item['product_name'] as String;
      final quantity = item['total_quantity'] as num;
      final unitPrice = item['unit_price'] as num;
      final total = item['total_amount'] as num;
      
      if (consolidatedProducts.containsKey(productName)) {
        // Add to existing product
        final existing = consolidatedProducts[productName]!;
        final totalQty = (existing['total_quantity'] as num) + quantity;
        final totalAmount = (existing['total_amount'] as num) + total;
        final avgPrice = totalAmount / totalQty;
        
        consolidatedProducts[productName] = {
          'product_name': productName,
          'total_quantity': totalQty,
          'unit_price': avgPrice,
          'total_amount': totalAmount,
        };
      } else {
        // New product
        consolidatedProducts[productName] = {
          'product_name': productName,
          'total_quantity': quantity,
          'unit_price': unitPrice,
          'total_amount': total,
        };
      }
    }
    
    final consolidatedList = consolidatedProducts.values.toList();
    final zFile = StringBuffer();

    // Remove ESC/POS formatting codes and center company name
    final companyName = (_companySettings?.name.toUpperCase() ?? 'POS SYSTEM');
    final totalWidth = 40;
    final centeredCompany = companyName.padLeft(((totalWidth + companyName.length) ~/ 2)).padRight(totalWidth);
    zFile.writeln(centeredCompany);
    zFile.writeln('Z-FILE SUMMARY REPORT');
    zFile.writeln('');

    if (_companySettings?.address != null) {
      zFile.writeln(_companySettings!.address);
    }
    if (_companySettings?.phone != null) {
      zFile.writeln('Tel: ${_companySettings!.phone}');
    }

    zFile.writeln('');
    zFile.writeln('Date: $formattedDate');
    zFile.writeln('Time: $formattedTime');
    zFile.writeln('Cashier: ${widget.cashierName}');
    zFile.writeln('Report Type: Z-FILE SUMMARY');
    zFile.writeln('');

    // Sales Summary
    zFile.writeln('SALES SUMMARY');
    zFile.writeln('=' * 40);
    zFile.writeln('Cash Sales:'.padRight(25) + '\$${cashSales.toStringAsFixed(2)}');
    zFile.writeln('Bank Transfer Sales:'.padRight(25) + '\$${bankSales.toStringAsFixed(2)}');
    zFile.writeln('-' * 40);
    zFile.writeln('TOTAL SALES:'.padRight(25) + '\$${totalSales.toStringAsFixed(2)}');
    zFile.writeln('');

    // Product Details
    zFile.writeln('PRODUCT SALES DETAILS');
    zFile.writeln('=' * 40);
    zFile.writeln('PRODUCT NAME'.padRight(20) + 'QTY'.padLeft(7) + 'TOTAL'.padLeft(11));
    zFile.writeln('-' * 40);

    if (consolidatedList.isNotEmpty) {
      for (var item in consolidatedList) {
        final productName = (item['product_name'] as String).padRight(20);
        final quantity = (item['total_quantity'] as num).toString().padLeft(7);
        final total = (item['total_amount'] as num).toStringAsFixed(2).padLeft(11);
        zFile.writeln('$productName$quantity$total');
      }
    } else {
      zFile.writeln('No products sold today.'.padLeft(20));
    }

    zFile.writeln('-' * 40);
    
    // Calculate and display product totals
    if (consolidatedList.isNotEmpty) {
      final productTotal = consolidatedList.fold<double>(0.0, (sum, item) => sum + (item['total_amount'] as num));
      zFile.writeln('PRODUCT TOTAL:'.padRight(25) + '\$${productTotal.toStringAsFixed(2)}');
    }
    
    zFile.writeln('');

    // Footer
    zFile.writeln('END OF Z-FILE SUMMARY');
    zFile.writeln('Generated by AllanWebApp-Cybrox');
    zFile.writeln('=' * 40);

    // Add multiple line feeds and cut paper
    for (int i = 0; i < 8; i++) {
      zFile.writeln('');
    }

    zFile.writeCharCode(0x1D);
    zFile.writeCharCode(0x56);
    zFile.writeCharCode(0x00);

    return zFile.toString();
  }

  Future<void> _printZFileSummary(String zFileText) async {
    try {
      if (Platform.isAndroid) {
        final profile = await CapabilityProfile.load();
        final generator = Generator(PaperSize.mm80, profile);
        final List<int> bytes = generator.text(zFileText);
        await printReceiptWithDefaultPrinter(bytes, context);
        return;
      } else if (Platform.isWindows) {
        final prefs = await SharedPreferences.getInstance();
        final printerName = prefs.getString('selected_printer') ?? 'POS-80C';
        await _directPrintToPOS80WithName(zFileText, printerName);
        return;
      } else {
        debugPrint('Z-File Summary (Debug Print):');
        debugPrint(zFileText);
      }
    } catch (e) {
      debugPrint('Z-File printing failed: $e');
      rethrow;
    }
  }

  void _onBarcodeScanned(String barcode) {
    final product = _products.firstWhere(
      (p) => p.barcode == barcode,
      orElse: () => Product(
        id: null,
        name: '',
        description: '',
        price: 0.0,
        quantity: 0.0,
        isTaxed: false,
        batchNumber: '',
        cost: 0.0,
        taxRate: 0.0,
        barcode: null,
      ),
    );
    if (product.id != null && product.barcode == barcode) {
      _addToCart(product);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Added ${product.name} to cart via barcode scan')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No product found for scanned barcode')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isWide = MediaQuery.of(context).size.width > 700;
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final isAndroid = Platform.isAndroid;
    
    final theme = _isDarkMode
        ? ThemeData.dark().copyWith(
            primaryColor: _darkPrimary,
            colorScheme: ColorScheme.dark(secondary: _darkAccent),
            scaffoldBackgroundColor: _darkBackground,
            textTheme: ThemeData.dark().textTheme.apply(fontFamily: 'Roboto'),
          )
        : ThemeData.light().copyWith(
            primaryColor: _lightBluePrimary,
            colorScheme: ColorScheme.light(secondary: _lightBlueAccent),
            scaffoldBackgroundColor: _lightBackground,
            textTheme: ThemeData.light().textTheme.apply(fontFamily: 'Roboto'),
          );

    return Theme(
      data: theme,
      child: Scaffold(
        appBar: AppBar(
          title: Row(
            children: [
              // Show total sales for today on the left
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.attach_money, size: 16, color: Colors.green),
                    const SizedBox(width: 2),
                    Text(
                      'Today: ',
                      style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                    ),
                    Text(
                      '\$${_todayTotalSales.toStringAsFixed(2)}',
                      style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.green),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Text('${_companySettings?.name ?? 'Allan Web App Mobile POS'}', style: const TextStyle(fontWeight: FontWeight.bold)),
              const Spacer(),
              Text('Lite v3.0', style: const TextStyle(fontStyle: FontStyle.italic, fontSize: 14)),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Cashier: ${widget.cashierName}',
                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.qr_code_scanner),
              onPressed: () async {
                final prefs = await SharedPreferences.getInstance();
                final hasSeenPermissionDialog = prefs.getBool('seen_camera_permission_dialog') ?? false;
                if (!hasSeenPermissionDialog) {
                  final accepted = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Camera Permission Required'),
                      content: const Text('This app needs camera access to scan product barcodes. The camera will only be used for scanning and nothing else.'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context, false),
                          child: const Text('Deny'),
                        ),
                        ElevatedButton(
                          onPressed: () => Navigator.pop(context, true),
                          child: const Text('Allow'),
                        ),
                      ],
                    ),
                  );
                  if (accepted != true) return;
                  await prefs.setBool('seen_camera_permission_dialog', true);
                }
                final status = await Permission.camera.request();
                if (!status.isGranted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Camera permission is required to scan barcodes.')),
                  );
                  return;
                }
                final barcode = await Navigator.push<String?>(
                  context,
                  MaterialPageRoute(builder: (_) => BarcodeScannerScreen()),
                );
                if (barcode != null && barcode.isNotEmpty) {
                  _onBarcodeScanned(barcode);
                }
              },
              tooltip: 'Scan Barcode',
            ),
            IconButton(
              icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
              onPressed: _toggleTheme,
              tooltip: _isDarkMode ? 'Light Mode' : 'Dark Mode',
            ),
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: _logout,
              tooltip: 'Logout',
            ),
            if (Platform.isAndroid)
              IconButton(
                icon: Icon(Icons.print),
                tooltip: 'Test Print',
                onPressed: () async {
                  final profile = await CapabilityProfile.load();
                  final generator = Generator(PaperSize.mm80, profile);
                  final List<int> bytes = generator.text('*** TEST PRINT ***\nDate: ${DateTime.now()}\nThank you for using AllanWebApp POS!\n\n');
                  await printReceiptWithDefaultPrinter(bytes, context);
                },
              ),
          ],
          elevation: 0,
          backgroundColor: theme.primaryColor,
        ),
        body: SafeArea(
          child: Stack(
            children: [
              Positioned.fill(
                child: Image.asset(
                  'assets/images/lite1.png',
                  fit: BoxFit.cover,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                child: _buildResponsiveLayout(isWide, isLandscape, isAndroid),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveLayout(bool isWide, bool isLandscape, bool isAndroid) {
    final veryCompact = _isVeryCompactScreen(context);
    // Desktop/Tablet wide layout
    if (isWide) {
      return Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              children: [
                _buildHeader(context, compact: false, veryCompact: false),
                _buildAutocompleteSearch(compact: false, veryCompact: false),
                Expanded(child: _buildProductGrid(context, isWide, compact: false, veryCompact: false)),
              ],
            ),
          ),
          const VerticalDivider(width: 1),
          Expanded(
            flex: 2,
            child: _buildCartPanel(context, isWide, compact: false, veryCompact: false),
          ),
        ],
      );
    }
    // Mobile layout (Android and others): always show cart at bottom, no drag/expand
    return Column(
      children: [
        _buildHeader(context, compact: true, veryCompact: veryCompact),
        _buildAutocompleteSearch(compact: true, veryCompact: veryCompact),
        Expanded(child: _buildProductGrid(context, isWide, compact: true, veryCompact: veryCompact)),
        const Divider(height: 1),
        SizedBox(height: veryCompact ? 2 : 4),
        SizedBox(
          // Increase cart height for veryCompact screens to show at least 2 items
          height: MediaQuery.of(context).size.height * (veryCompact ? 0.34 : 0.32),
          child: _buildCartPanel(context, isWide, compact: true, veryCompact: veryCompact),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, {bool compact = false, bool veryCompact = false}) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final double fontSize = veryCompact ? 10 : (compact ? 11 : (isLandscape ? 12 : 14));
    final double userFontSize = veryCompact ? 9 : (compact ? 10 : (isLandscape ? 11 : 13));
    final double verticalPad = veryCompact ? 1 : (compact ? 2 : (isLandscape ? 2 : 4));
    final double horizontalPad = veryCompact ? 2 : (compact ? 4 : (isLandscape ? 4 : 8));
    return Padding(
      padding: EdgeInsets.symmetric(vertical: verticalPad, horizontal: 2),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.85),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.symmetric(
          vertical: verticalPad,
          horizontal: horizontalPad,
        ),
        child: Row(
          children: [
            DropdownButton<String>(
              value: _searchFilterType,
              items: const [
                DropdownMenuItem(value: 'Name', child: Text('Name')),
                DropdownMenuItem(value: 'Barcode', child: Text('Barcode')),
              ],
              onChanged: (value) {
                setState(() {
                  _searchFilterType = value!;
                  _filterProducts();
                });
              },
              style: TextStyle(fontSize: fontSize),
            ),
            SizedBox(width: veryCompact ? 1 : (compact ? 2 : (isLandscape ? 4 : 8))),
            Expanded(
              child: TextField(
                controller: _searchController,
                style: TextStyle(fontSize: fontSize),
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.symmetric(
                    vertical: veryCompact ? 2 : (compact ? 4 : (isLandscape ? 6 : 8)),
                    horizontal: veryCompact ? 2 : (compact ? 4 : (isLandscape ? 6 : 8)),
                  ),
                  labelText: 'Search',
                  prefixIcon: Icon(
                    Icons.search,
                    size: veryCompact ? 12 : (compact ? 14 : (isLandscape ? 16 : 20)),
                  ),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  isDense: true,
                ),
              ),
            ),
            SizedBox(width: veryCompact ? 1 : (compact ? 2 : (isLandscape ? 4 : 8))),
            Text(
              'User: ${widget.cashierName}',
              style: TextStyle(
                fontSize: userFontSize,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutocompleteSearch({bool compact = false, bool veryCompact = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: veryCompact ? 1 : (compact ? 2 : 4)),
      child: Autocomplete<Product>(
        optionsBuilder: (TextEditingValue textEditingValue) {
          if (textEditingValue.text.isEmpty) {
            return const Iterable<Product>.empty();
          }
          final query = textEditingValue.text.toLowerCase();
          return _products.where((Product product) {
            return product.name.toLowerCase().contains(query) ||
                (product.barcode != null && product.barcode!.toLowerCase().contains(query));
          });
        },
        displayStringForOption: (Product option) =>
            option.name + (option.barcode != null && option.barcode!.isNotEmpty ? ' (${option.barcode})' : ''),
        fieldViewBuilder: (context, controller, focusNode, onFieldSubmitted) {
          return TextField(
            controller: controller,
            focusNode: focusNode,
            decoration: InputDecoration(
              labelText: 'Quick Add Product',
              prefixIcon: Icon(Icons.add_shopping_cart, size: veryCompact ? 13 : (compact ? 16 : 20)),
              border: const OutlineInputBorder(),
              isDense: true,
            ),
            style: TextStyle(fontSize: veryCompact ? 10 : (compact ? 12 : 14)),
          );
        },
        onSelected: (Product selectedProduct) {
          _addToCart(selectedProduct);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Added ${selectedProduct.name} to cart')),
          );
        },
      ),
    );
  }

  Widget _buildProductGrid(BuildContext context, bool isWide, {bool compact = false, bool veryCompact = false}) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    int crossAxisCount;
    double childAspectRatio;
    if (isWide) {
      crossAxisCount = 6;
      childAspectRatio = 0.85;
    } else if (isLandscape) {
      crossAxisCount = veryCompact ? 4 : (compact ? 3 : 4);
      childAspectRatio = veryCompact ? 1.1 : (compact ? 1.0 : 1.2);
    } else {
      crossAxisCount = veryCompact ? 3 : (compact ? 2 : 3);
      childAspectRatio = veryCompact ? 0.95 : (compact ? 0.8 : 0.85);
    }
    return GridView.builder(
      padding: EdgeInsets.all(veryCompact ? 1 : (compact ? 2 : 4)),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: veryCompact ? 2 : (compact ? 4 : 6),
        mainAxisSpacing: veryCompact ? 2 : (compact ? 4 : 6),
      ),
      itemCount: _filteredProducts.length,
      itemBuilder: (context, index) {
        final product = _filteredProducts[index];
        final isTaxed = product.isTaxed && product.taxRate > 0;
        final displayPrice = isTaxed
            ? product.price * (1 + product.taxRate / 100)
            : product.price;
        return Card(
          margin: EdgeInsets.zero,
          elevation: 1,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: () => _addToCart(product),
            onLongPress: () => _showProductDialog(product),
            child: Padding(
              padding: EdgeInsets.all(veryCompact ? 2.0 : (compact ? 4.0 : 6.0)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    product.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: veryCompact ? 9 : (compact ? 11 : (isLandscape ? 12 : 14)),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: veryCompact ? 1 : (compact ? 2 : 4)),
                  if (product.barcode != null && product.barcode!.isNotEmpty && !isLandscape)
                    Text(
                      'Barcode: ${product.barcode}',
                      style: TextStyle(fontSize: veryCompact ? 7 : (compact ? 9 : 11), color: Colors.blueGrey),
                    ),
                  Text(
                    '${displayPrice.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: veryCompact ? 8 : (compact ? 10 : (isLandscape ? 11 : 13)),
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (isTaxed && !isLandscape)
                    Text(
                      'Incl. Tax (${product.taxRate}%)',
                      style: TextStyle(fontSize: veryCompact ? 7 : (compact ? 9 : 11), color: Colors.orange),
                    ),
                  Text(
                    'Stock: ${product.quantity}',
                    style: TextStyle(
                      fontSize: veryCompact ? 7 : (compact ? 9 : (isLandscape ? 10 : 12)),
                      color: product.quantity > 0 ? Colors.black54 : Colors.red,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCartPanel(BuildContext context, bool isWide, {bool compact = false, bool veryCompact = false}) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    return Card(
      margin: EdgeInsets.all(veryCompact ? 0.5 : (compact ? 1 : 2)),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: EdgeInsets.all(veryCompact ? 2 : (compact ? 4 : 8)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Icon(Icons.shopping_cart, size: veryCompact ? 11 : (compact ? 14 : (isLandscape ? 16 : 20))),
                SizedBox(width: veryCompact ? 2 : (compact ? 4 : 8)),
                Expanded(
                  child: Text(
                    'Current Sale',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: veryCompact ? 10 : (compact ? 12 : (isLandscape ? 14 : 16)),
                    ),
                  ),
                ),
              ],
            ),
            const Divider(height: 8),
            Expanded(child: _buildCartListCompact(compact: compact, veryCompact: veryCompact)),
            const Divider(height: 8),
            _buildTotalsSection(compact: compact, veryCompact: veryCompact),
            SizedBox(height: veryCompact ? 2 : (compact ? 4 : 8)),
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _processSale,
              icon: Icon(
                Icons.check_circle_outline,
                size: veryCompact ? 11 : (compact ? 14 : (isLandscape ? 16 : 20)),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: _lightBlueAccent,
                padding: EdgeInsets.symmetric(
                  vertical: veryCompact ? 4 : (compact ? 6 : (isLandscape ? 8 : 12)),
                ),
                textStyle: TextStyle(
                  fontSize: veryCompact ? 10 : (compact ? 12 : (isLandscape ? 14 : 16)),
                  fontWeight: FontWeight.bold,
                ),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              label: const Text('Process Sale'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartListCompact({bool compact = false, bool veryCompact = false}) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    if (_cart.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: veryCompact ? 16 : (compact ? 24 : (isLandscape ? 32 : 48)),
              color: Colors.grey[400],
            ),
            SizedBox(height: veryCompact ? 2 : (compact ? 4 : 8)),
            Text(
              'Cart is empty',
              style: TextStyle(
                fontSize: veryCompact ? 8 : (compact ? 10 : (isLandscape ? 12 : 14)),
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: veryCompact ? 1 : (compact ? 2 : 4)),
            Text(
              'Add products to get started',
              style: TextStyle(
                fontSize: veryCompact ? 7 : (compact ? 9 : (isLandscape ? 10 : 12)),
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }
    return ListView.separated(
      itemCount: _cart.length,
      separatorBuilder: (_, __) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final entry = _cart.entries.elementAt(index);
        final product = _products.firstWhere((p) => p.id == entry.key);
        final qty = entry.value;
        final isTaxed = product.isTaxed && product.taxRate > 0;
        final taxAmount = isTaxed ? (product.price * qty) * (product.taxRate / (100 + product.taxRate)) : 0;
        final totalWithTax = product.price * qty;
        return ListTile(
          dense: true,
          contentPadding: EdgeInsets.symmetric(
            horizontal: veryCompact ? 1 : (compact ? 2 : 4),
            vertical: veryCompact ? 0 : (compact ? 0 : (isLandscape ? 2 : 0)),
          ),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  product.name,
                  style: TextStyle(
                    fontSize: veryCompact ? 8 : (compact ? 10 : (isLandscape ? 11 : 13)),
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (isTaxed)
                Container(
                  margin: const EdgeInsets.only(left: 4),
                  padding: EdgeInsets.symmetric(horizontal: veryCompact ? 1 : (compact ? 2 : 4), vertical: 1),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Taxed',
                    style: TextStyle(
                      fontSize: veryCompact ? 6 : (compact ? 7 : (isLandscape ? 8 : 10)),
                      color: Colors.orange,
                    ),
                  ),
                ),
            ],
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${qty.toStringAsFixed(2)} x ${product.price.toStringAsFixed(2)}',
                style: TextStyle(fontSize: veryCompact ? 7 : (compact ? 9 : (isLandscape ? 10 : 12))),
              ),
              if (isTaxed)
                Text(
                  'Tax: ${taxAmount.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: veryCompact ? 6 : (compact ? 8 : (isLandscape ? 9 : 11)),
                    color: Colors.orange,
                  ),
                ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${totalWithTax.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: veryCompact ? 8 : (compact ? 10 : (isLandscape ? 11 : 13)),
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.edit,
                  size: veryCompact ? 10 : (compact ? 13 : (isLandscape ? 16 : 18)),
                ),
                tooltip: 'Edit quantity',
                onPressed: () => _showProductDialog(product),
              ),
              IconButton(
                icon: Icon(
                  Icons.delete,
                  size: veryCompact ? 10 : (compact ? 13 : (isLandscape ? 16 : 18)),
                ),
                tooltip: 'Remove from cart',
                onPressed: () => _removeFromCart(product),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTotalsSection({bool compact = false, bool veryCompact = false}) {
    final subtotal = _calculateSubtotal();
    final vat = _calculateVAT();
    final total = _calculateTotal();
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    return Container(
      padding: EdgeInsets.all(veryCompact ? 2 : (compact ? 4 : (isLandscape ? 4 : 8))),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Subtotal:', style: TextStyle(fontSize: veryCompact ? 8 : (compact ? 10 : (isLandscape ? 11 : 13)))),
              Text('${subtotal.toStringAsFixed(2)}', style: TextStyle(fontSize: veryCompact ? 8 : (compact ? 10 : (isLandscape ? 11 : 13)))),
            ],
          ),
          SizedBox(height: veryCompact ? 1 : (compact ? 1 : 2)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('VAT:', style: TextStyle(fontSize: veryCompact ? 8 : (compact ? 10 : (isLandscape ? 11 : 13)))),
              Text('${vat.toStringAsFixed(2)}', style: TextStyle(fontSize: veryCompact ? 8 : (compact ? 10 : (isLandscape ? 11 : 13)))),
            ],
          ),
          const Divider(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: veryCompact ? 9 : (compact ? 11 : (isLandscape ? 12 : 14)),
                  color: _lightBlueAccent,
                ),
              ),
              Text(
                '${total.toStringAsFixed(2)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: veryCompact ? 9 : (compact ? 11 : (isLandscape ? 12 : 14)),
                  color: _lightBlueAccent,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<String?> _showBarcodeInputDialog(BuildContext context) async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Scan or Enter Barcode'),
        content: TextField(
          controller: controller,
          autofocus: true,
          decoration: const InputDecoration(labelText: 'Barcode'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, null),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, controller.text.trim()),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Helper to detect very compact screens (Sunmi POS, 720px wide or less)
  bool _isVeryCompactScreen(BuildContext context) {
    return MediaQuery.of(context).size.width <= 720;
  }
}

class BarcodeScannerScreen extends StatefulWidget {
  @override
  State<BarcodeScannerScreen> createState() => _BarcodeScannerScreenState();
}

class _BarcodeScannerScreenState extends State<BarcodeScannerScreen> {
  bool _isProcessing = false;
  String? _error;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Barcode'),
        backgroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          MobileScanner(
            onDetect: (capture) async {
              if (_isProcessing) return;
              final barcodes = capture.barcodes;
              if (barcodes.isNotEmpty) {
                final code = barcodes.first.rawValue;
                if (code != null && code.isNotEmpty) {
                  setState(() => _isProcessing = true);
                  await Future.delayed(const Duration(milliseconds: 300));
                  Navigator.pop(context, code);
                }
              }
            },
            errorBuilder: (context, error, child) {
              return Center(
                child: Text(
                  'Camera error: $error',
                  style: const TextStyle(color: Colors.red, fontSize: 16),
                ),
              );
            },
          ),
          if (_isProcessing)
            Container(
              color: Colors.black54,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
          if (_error != null)
            Center(
              child: Text(
                _error!,
                style: const TextStyle(color: Colors.red, fontSize: 16),
              ),
            ),
        ],
      ),
    );
  }
}

// Add a helper for Windows to use the selected printer name
Future<void> _directPrintToPOS80WithName(String receiptText, String printerName) async {
  final hPrinter = calloc<HANDLE>();
  final docInfo = calloc<DOC_INFO_1>()
    ..ref.pDocName = 'POS Receipt'.toNativeUtf16()
    ..ref.pOutputFile = nullptr
    ..ref.pDatatype = 'RAW'.toNativeUtf16();

  final printerOpen = OpenPrinter(printerName.toNativeUtf16(), hPrinter, nullptr);
  if (printerOpen == 0) {
    debugPrint('Failed to open printer $printerName: \\${GetLastError()}');
    calloc.free(hPrinter);
    calloc.free(docInfo);
    return;
  }

  if (StartDocPrinter(hPrinter.value, 1, docInfo.cast()) == 0) {
    debugPrint('Failed to start document: \\${GetLastError()}');
    ClosePrinter(hPrinter.value);
    calloc.free(hPrinter);
    calloc.free(docInfo);
    return;
  }

  StartPagePrinter(hPrinter.value);

  final receiptBytes = Uint8List.fromList(receiptText.codeUnits);
  final pBytes = calloc<Uint8>(receiptBytes.length);
  pBytes.asTypedList(receiptBytes.length).setAll(0, receiptBytes);

  final bytesWritten = calloc<DWORD>();
  WritePrinter(hPrinter.value, pBytes.cast(), receiptBytes.length, bytesWritten);

  EndPagePrinter(hPrinter.value);
  EndDocPrinter(hPrinter.value);
  ClosePrinter(hPrinter.value);

  calloc.free(pBytes);
  calloc.free(bytesWritten);
  calloc.free(hPrinter);
  calloc.free(docInfo);
}

// Add this function to print using the default printer
Future<void> printReceiptWithDefaultPrinter(List<int> ticketBytes, BuildContext context) async {
  final prefs = await SharedPreferences.getInstance();
  final printerName = prefs.getString('default_printer_name');
  final printerAddress = prefs.getString('default_printer_address');
  if (printerName == null || printerAddress == null) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('No default printer set. Please select a printer in settings.')),
    );
    return;
  }

  final printerManager = PrinterBluetoothManager();
  PrinterBluetooth? selectedPrinter;

  // Start scan and wait for results
  printerManager.startScan(const Duration(seconds: 2));
  await Future.delayed(const Duration(seconds: 2)); // Wait for scan to complete

  final devices = await printerManager.scanResults.first;
  final foundPrinter = devices.where((d) => d.address == printerAddress);
  selectedPrinter = foundPrinter.isNotEmpty ? foundPrinter.first : null;

  if (selectedPrinter == null) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Saved printer not found. Please re-select in settings.')),
    );
    return;
  }

  printerManager.selectPrinter(selectedPrinter);
  try {
    await printerManager.printTicket(ticketBytes);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Print sent to $printerName')),
    );
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Print failed: $e')),
    );
  }
}