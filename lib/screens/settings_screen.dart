import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../services/printer_service.dart';
import '../models/pos_models.dart';
import 'security_settings_screen.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import '../widgets/printer_settings_widget.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final DatabaseService _db = DatabaseService.instance;

  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _taxNumberController = TextEditingController();
  final _receiptFooterController = TextEditingController();

  // User management form controllers
  final _userFormKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _userFullnameController = TextEditingController();
  final _userPhoneController = TextEditingController();
  String _selectedRole = 'cashier';
  final TextEditingController _newPrinterController = TextEditingController();

  // Printer settings
  final List<String> _printerList = [];

  // Bluetooth printer selection
  String? _selectedPrinterName;
  final PrinterService _printerService = PrinterService();

  List<Map<String, dynamic>> _users = [];
  bool _isLoading = false;

  static const List<String> _validLicenseKeys = [
    'BZDE-12045-FECI-61290',
    'XKLD-84920-ZMNT-14736',
    'PLAQ-26374-YOFR-95812',
    'GHTY-52983-WXNB-10647',
    'VFDR-87412-LMKC-35970',
    'ZNOP-63582-QWET-79431',
    'YUIK-48753-BCXZ-21069',
    'OPQA-31569-VFRT-80247',
    'JHGF-97208-MNBL-65413',
    'ERTY-65983-XCVB-14027',
    'POIU-74156-AQWE-39820',
    'LKJM-84207-DRTY-65913',
    'XBNM-69521-FGHJ-87034',
    'PLMK-30974-WEQA-62581',
    'AQSW-52891-ZXCV-30764',
    'VCXZ-84750-QWER-62913',
    'TYUI-30948-OPLK-52781',
    'BNMA-62174-HGFJ-93850',
    'JHUI-94857-MNVC-16230',
    'FGHJ-47826-PLKO-95371',
    'YTRD-72951-WEFG-80642',
    'GHJK-51028-XMNB-74396',
    'MNBV-62831-RTYU-50974',
    'ZXCV-97463-OPLK-25108',
    'QWER-30854-TYUI-69527',
    'ABCD-12345-EFGH-67890',
    'IJKL-23456-MNOP-78901',
    'QRST-34567-UVWX-89012',
    'YZAB-45678-CDEF-90123',
    'GHIJ-56789-KLMN-01234',
    'OPQR-67890-STUV-12345',
    'WXYZ-78901-ABCD-23456',
    'EFGH-89012-IJKL-34567',
    'MNOP-90123-QRST-45678',
    'UVWX-01234-YZAB-56789',
    'FZTA-12478-LZXC-83920',
    'NTRE-58293-QLWI-20847',
    'ZMBX-93714-KUYR-17583',
    'VRPL-29483-MNBQ-56721',
    'CHUI-37594-DGFE-19038',
    'HEUI-23847-PLMA-82375',
    'OEIR-39024-XNCB-12934',
    'RYET-59401-KJYT-94230',
    'MZLP-84302-SAQW-67583',
    'TGBY-29374-VCFD-89310',
    'QAZX-68290-SDWE-37458',
    'PLOI-91283-MNVC-48372',
    'IUYT-83746-KLJD-28493',
    'LKMN-50283-QWER-19032',
    'VBNM-98420-XCAS-47201',
    'ZXMN-28374-POIU-98401',
    'JUIK-38420-VFRE-28390',
    'QWER-19283-TGBN-37485',
    'HYGT-49283-PLKM-21038',
    'GHUE-37201-IOPL-38591',
    'NMVC-83746-KJHF-28192',
    'PLQW-12038-VFRX-58201',
    'BNVC-94820-ASWE-30291',
    'YUIO-58302-LKJM-84721',
    'TRDE-12938-MZXC-58230',
    'WESA-94820-ZXPO-37485',
    'UXME-18294-WQAS-47302',
  ];
  static final List<String> _validLicenseHashes = _validLicenseKeys.map((k) => sha256.convert(utf8.encode(k)).toString()).toList();

  final List<String> _userLicenses = [];
  String? _licenseStatus;
  final TextEditingController _licenseController = TextEditingController();


  // Remove legacy printer list
  // Printer selection handled by BluetoothPrinterService


  static const String _dbEncryptionKey = 'cybrox_lite_secure_db_key_2024';

  // Bluetooth printer service instance already initialized above


  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadSettings();
    _loadUsers();
    _loadPrinters();
    _checkLicenseStatus();
    // Initialize printer service
    _printerService.init();
  }

 


  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    try {
      final settings = await _db.getCompanySettings();
      if (settings != null) {
        _nameController.text = settings.name;
        _addressController.text = settings.address;
        _phoneController.text = settings.phone;
        _emailController.text = settings.email;
        _taxNumberController.text = settings.taxNumber;
        _receiptFooterController.text = settings.receiptFooter;
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }


  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      final settings = CompanySettings(
        name: _nameController.text,
        address: _addressController.text,
        phone: _phoneController.text,
        email: _emailController.text,
        taxNumber: _taxNumberController.text,
        receiptFooter: _receiptFooterController.text,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _db.saveCompanySettings(settings);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Settings saved successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving settings: $e')),
      );
      print('Error Saving company: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  final _printerNameController = TextEditingController();
  
  Object? get _selectedPrinter => _printerService.selectedPrinter;



  Future<void> _loadUsers() async {
    setState(() => _isLoading = true);
    try {
      final users = await _db.getAllUsers();
      setState(() => _users = users);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveUser() async {
    if (!_userFormKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      // Generate salt
      final salt = _generateSalt();
      // Hash the password with salt and encryption key
      final password = _passwordController.text;
      final combined = password + salt + _dbEncryptionKey;
      final passwordHash = sha256.convert(utf8.encode(combined)).toString();
      final user = {
        'username': _usernameController.text,
        'password_hash': passwordHash,
        'password_salt': salt,
        'fullname': _userFullnameController.text,
        'phone': _userPhoneController.text,
        'role': _selectedRole,
      };

      final success = await _db.createUser(user);
      if (!success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error creating user. Please check your input and try again.')),
        );
      } else {
        _clearUserForm();
        await _loadUsers();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('User created successfully')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error creating user: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _clearUserForm() {
    _usernameController.clear();
    _passwordController.clear();
    _userFullnameController.clear();
    _userPhoneController.clear();
    _selectedRole = 'cashier';
  }

  Future<void> _deleteUser(int id) async {
    try {
      await _db.deleteUser(id);
      await _loadUsers();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User deleted successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting user: $e')),
      );
    }
  }

  Future<void> _addLicense() async {
    final enteredKey = _licenseController.text.trim();
    final hash = sha256.convert(utf8.encode(enteredKey)).toString();
    if (_validLicenseHashes.contains(hash)) {
      await DatabaseService.instance.insertLicense(enteredKey);
      await _checkLicenseStatus();
      setState(() {
        _userLicenses.add(hash);
        _licenseStatus = 'Valid and Activated';
      });
    } else {
      setState(() {
        _licenseStatus = 'Invalid License Key';
      });
    }
  }

  Future<void> _checkLicenseStatus() async {
    final status = await DatabaseService.instance.checkLicenseStatus();
    if (status['status'] == 'active') {
      setState(() {
        _licenseStatus = 'License valid. Days left: ${status['days_left']}';
      });
    } else if (status['status'] == 'expired') {
      setState(() {
        _licenseStatus = 'License expired. Please renew.';
      });
    } else if (status['status'] == 'time_manipulation') {
      setState(() {
        _licenseStatus = 'Device time manipulation detected! Please enter a new license.';
      });
    } else {
      setState(() {
        _licenseStatus = 'No active license.';
      });
    }
  }

  Future<void> _renewLicense() async {
    final enteredKey = _licenseController.text.trim();
    final hash = sha256.convert(utf8.encode(enteredKey)).toString();
    if (_validLicenseHashes.contains(hash)) {
      await DatabaseService.instance.renewLicense(enteredKey);
      await _checkLicenseStatus();
      setState(() {
        _licenseStatus = 'License renewed!';
      });
    } else {
      setState(() {
        _licenseStatus = 'Invalid License Key for renewal.';
      });
    }
  }

  void _showEditUserDialog(Map<String, dynamic> user) {
    _usernameController.text = user['username'] ?? '';
    _passwordController.text = user['password'] ?? '';
    _userFullnameController.text = user['fullname'] ?? '';
    _userPhoneController.text = user['phone'] ?? '';
    _selectedRole = user['role'] ?? 'cashier';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Edit User'),
          content: Form(
            key: _userFormKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: _usernameController,
                    decoration: const InputDecoration(labelText: 'Username'),
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Please enter username' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _passwordController,
                    decoration: const InputDecoration(labelText: 'Password'),
                    obscureText: true,
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Please enter password' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _userFullnameController,
                    decoration: const InputDecoration(labelText: 'Full Name'),
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Please enter full name' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _userPhoneController,
                    decoration: const InputDecoration(labelText: 'Phone'),
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Please enter phone number' : null,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedRole,
                    decoration: const InputDecoration(labelText: 'Role'),
                    items: const [
                      DropdownMenuItem(value: 'admin', child: Text('Admin')),
                      DropdownMenuItem(value: 'cashier', child: Text('Cashier')),
                    ],
                    onChanged: (value) {
                      if (context.mounted) {
                        setState(() {
                          _selectedRole = value!;
                        });
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearUserForm();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (_userFormKey.currentState!.validate()) {
                  await _updateUser(user['id']);
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Save Changes'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _updateUser(int id) async {
    setState(() => _isLoading = true);
    try {
      // Generate new salt and hash if password is changed
      final salt = _generateSalt();
      final password = _passwordController.text;
      final combined = password + salt + _dbEncryptionKey;
      final passwordHash = sha256.convert(utf8.encode(combined)).toString();
      final updatedUser = {
        'id': id,
        'username': _usernameController.text,
        'password_hash': passwordHash,
        'password_salt': salt,
        'fullname': _userFullnameController.text,
        'phone': _userPhoneController.text,
        'role': _selectedRole,
      };
      await _db.updateUser(id, updatedUser);
      _clearUserForm();
      await _loadUsers();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User updated successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating user: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _taxNumberController.dispose();
    _receiptFooterController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _userFullnameController.dispose();
    _userPhoneController.dispose();
    _licenseController.dispose();
    _printerNameController.dispose();
    _newPrinterController.dispose();
    super.dispose();
  }

 // PrinterBluetooth? _selectedPrinter;

  Widget _buildCompanySettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'Company Information',
                      style: Theme.of(context).textTheme.titleLarge,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _nameController,
                            decoration: const InputDecoration(
                              labelText: 'Company Name',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.business),
                            ),
                            validator: (value) =>
                                value?.isEmpty ?? true ? 'Please enter company name' : null,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _taxNumberController,
                            decoration: const InputDecoration(
                              labelText: 'Tax Number',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.receipt_long),
                            ),
                            validator: (value) =>
                                value?.isEmpty ?? true ? 'Please enter tax number' : null,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        labelText: 'Address',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_on),
                      ),
                      validator: (value) =>
                          value?.isEmpty ?? true ? 'Please enter address' : null,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _phoneController,
                            decoration: const InputDecoration(
                              labelText: 'Phone',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.phone),
                            ),
                            validator: (value) =>
                                value?.isEmpty ?? true ? 'Please enter phone number' : null,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _emailController,
                            decoration: const InputDecoration(
                              labelText: 'Email',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.email),
                            ),
                            validator: (value) =>
                                value?.isEmpty ?? true ? 'Please enter email' : null,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _receiptFooterController,
                      decoration: const InputDecoration(
                        labelText: 'Receipt Footer Message',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.message),
                      ),
                      validator: (value) =>
                          value?.isEmpty ?? true ? 'Please enter receipt footer' : null,
                      maxLines: 3,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _saveSettings,
                      icon: const Icon(Icons.save),
                      label: const Text('Save Settings'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserManagementTab() {
    return Stack(
      children: [
        SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text('Existing Users',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              DataTable(
                columns: const [
                  DataColumn(label: Text('Full Name')),
                  DataColumn(label: Text('Username')),
                  DataColumn(label: Text('Role')),
                  DataColumn(label: Text('Actions')),
                ],
                rows: _users.map((user) {
                  return DataRow(
                    cells: [
                      DataCell(Text(user['fullname'])),
                      DataCell(Text(user['username'])),
                      DataCell(Text(user['role'])),
                      DataCell(Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _showEditUserDialog(user),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _deleteUser(user['id']),
                          ),
                        ],
                      )),
                    ],
                  );
                }).toList(),
              ),
              const SizedBox(height: 80), // For FAB spacing
            ],
          ),
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: FloatingActionButton(
            onPressed: () => _showAddUserDialog(),
            child: const Icon(Icons.add),
            tooltip: 'Add User',
          ),
        ),
      ],
    );
  }

  void _showAddUserDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Add New User'),
          content: Form(
            key: _userFormKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: _usernameController,
                    decoration: const InputDecoration(labelText: 'Username'),
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Please enter username' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _passwordController,
                    decoration: const InputDecoration(labelText: 'Password'),
                    obscureText: true,
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Please enter password' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _userFullnameController,
                    decoration: const InputDecoration(labelText: 'Full Name'),
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Please enter full name' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _userPhoneController,
                    decoration: const InputDecoration(labelText: 'Phone'),
                    validator: (value) =>
                        value?.isEmpty ?? true ? 'Please enter phone number' : null,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedRole,
                    decoration: const InputDecoration(labelText: 'Role'),
                    items: const [
                      DropdownMenuItem(value: 'admin', child: Text('Admin')),
                      DropdownMenuItem(value: 'cashier', child: Text('Cashier')),
                    ],
                    onChanged: (value) {
                      if (context.mounted) {
                        setState(() {
                          _selectedRole = value!;
                        });
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearUserForm();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (_userFormKey.currentState!.validate()) {
                  await _saveUser();
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Create User'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPrinterSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPrinterName ?? (_printerList.isNotEmpty ? _printerList.first : null),
                          items: _printerList
                              .map((printer) => DropdownMenuItem(
                                    value: printer,
                                    child: Text(printer),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPrinterName = value;
                              // Optionally, update _selectedPrinter if you want to keep the PrinterBluetooth object in sync
                            });
                          },
                          decoration: const InputDecoration(
                            labelText: 'Select Printer',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.save),
                        tooltip: 'Save Selected Printer',
                        onPressed: _saveSelectedPrinter,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _newPrinterController,
                    decoration: const InputDecoration(
                      labelText: 'Add New Printer',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.add),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _addPrinter,
                    child: const Text('Add Printer'),
                  ),
                  const SizedBox(height: 16),
                  Text('Selected Printer: $_selectedPrinter'),
                  const SizedBox(height: 16),
                  PrinterSettingsWidget(),
                  if (Platform.isWindows && _printerList.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),
                        const Text('System Printers:', style: TextStyle(fontWeight: FontWeight.bold)),
                        ..._printerList.map((p) => ListTile(
                              title: Text(p),
                              trailing: _selectedPrinterName == p
                                  ? const Icon(Icons.check, color: Colors.green)
                                  : null,
                              onTap: () {
                                setState(() {
                                  _selectedPrinterName = p;
                                  _printerNameController.text = p;
                                });
                              },
                            )),
                      ],
                    ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testPrint,
                    icon: const Icon(Icons.print),
                    label: const Text('Test Print'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }


  Future<void> _loadPrinters() async {
    // No-op: Bluetooth printers are managed by BluetoothPrinterService
  }

  Future<void> _addPrinter() async {
    final newPrinter = _newPrinterController.text.trim();
    if (newPrinter.isEmpty || _printerList.contains(newPrinter)) return;
    final prefs = await SharedPreferences.getInstance();
    final userPrinters = prefs.getStringList('user_printers') ?? [];
    userPrinters.add(newPrinter);
    await prefs.setStringList('user_printers', userPrinters);
    setState(() {
      _printerList.add(newPrinter);
      _selectedPrinterName = newPrinter;
      _printerNameController.text = newPrinter;
    });
    await prefs.setString('selected_printer', newPrinter);
    _newPrinterController.clear();
  }

  Future<void> _saveSelectedPrinter() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selected_printer', _selectedPrinterName!);
    setState(() {
      _printerNameController.text = _selectedPrinterName!;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Selected printer saved!')),
    );
  }

  Widget _buildLicenseTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'License Status',
                    style: Theme.of(context).textTheme.titleLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _licenseStatus ?? 'Checking license...',
                    style: TextStyle(
                      color: (_licenseStatus != null && _licenseStatus!.contains('valid'))
                          ? Colors.green
                          : Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  if (_licenseStatus == null ||
                      _licenseStatus!.contains('expired') ||
                      _licenseStatus!.contains('Invalid') ||
                      _licenseStatus!.contains('No active license') ||
                      _licenseStatus!.contains('time manipulation'))
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        TextFormField(
                          controller: _licenseController,
                          decoration: const InputDecoration(
                            labelText: 'Enter License Key',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.vpn_key),
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _addLicense,
                          icon: const Icon(Icons.check),
                          label: const Text('Activate License'),
                        ),
                      ],
                    )
                  else ...[
                    ElevatedButton.icon(
                      onPressed: _renewLicense,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Renew License'),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).primaryColor,
          tabs: const [
            Tab(text: 'Company Settings'),
            Tab(text: 'User Management'),
            Tab(text: 'Printer Settings'),
            Tab(text: 'License'),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildCompanySettingsTab(),
              _buildUserManagementTab(),
              _buildPrinterSettingsTab(),
              _buildLicenseTab(),
            ],
          ),
        ),
        // Security Settings Button
        Container(
          padding: EdgeInsets.all(16),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SecuritySettingsScreen(),
                  ),
                );
              },
              icon: Icon(Icons.security, color: Colors.white),
              label: Text('Security Settings', style: TextStyle(color: Colors.white)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[800],
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Define the buildFullReceipt method
  


  // Add a helper for salt generation
  String _generateSalt([int length = 16]) {
    final rand = List<int>.generate(length, (i) => (DateTime.now().microsecondsSinceEpoch + i) % 256);
    return base64Url.encode(rand);
  }

  Future<void> _testPrint() async {
    setState(() => _isLoading = true);
    try {
      await _printerService.printTestReceipt();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Test print sent successfully'))
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Test print failed: $e'))
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

}
