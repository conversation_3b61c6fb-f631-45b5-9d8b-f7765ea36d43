import 'dart:io';
import 'dart:convert';
import 'package:sunmi_printer_plus/sunmi_printer_plus.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:shared_preferences/shared_preferences.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sunmi_printer_plus/core/sunmi/sunmi_printer.dart';
import 'package:allanwebapp_mobile_pos/screens/cashier_screen.dart';
import '../services/database_service.dart';
import '../services/printer_service.dart';
import '../models/pos_models.dart';

class ProductsScreen extends StatefulWidget {
  const ProductsScreen({Key? key}) : super(key: key);

  @override
  _ProductsScreenState createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> with SingleTickerProviderStateMixin {
  final DatabaseService _db = DatabaseService.instance;
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _quantityController = TextEditingController();
  final _costController = TextEditingController();
  final _barcodeController = TextEditingController();
  bool _isTaxed = false;
  bool _isLoading = false;
  bool _isDarkMode = false;
  double _taxRate = 0.0;

  List<Product> _products = [];
  List<Product> _filteredProducts = [];
  List<Sale> _sales = [];
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 7));
  DateTime _endDate = DateTime.now();

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadProducts();
    _loadSales();
  }

  Future<void> _loadProducts() async {
    setState(() => _isLoading = true);
    try {
      final products = await _db.getAllProducts();
      setState(() {
        _products = products;
        _filteredProducts = products;
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

Future<void> _loadSales() async {
  setState(() => _isLoading = true);
  try {
    final sales = await _db.getSalesByDateRange(_startDate, _endDate);
    
    // Reverse the list to show newest sales first
    setState(() => _sales = sales.reversed.toList());
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Error loading sales: $e')),
    );
  } finally {
    setState(() => _isLoading = false);
  }
}


  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      final product = Product(
        name: _nameController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        quantity: double.parse(_quantityController.text),
        isTaxed: _isTaxed,
        batchNumber: Product.generateBatchNumber(),
        cost: double.parse(_costController.text),
        taxRate: _isTaxed ? _taxRate : 0.0,
      );

      await _db.insertProduct(product);
      await _loadProducts();
      Navigator.of(context).pop();
      _clearForm();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Product saved successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving product check: $e')),
      );
      print(e);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateProduct(Product product) async {
    setState(() => _isLoading = true);
    try {
      await _db.updateProduct(product);
      await _loadProducts();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Product updated successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating product: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteProduct(Product product) async {
    try {
      await _db.deleteProduct(product.id!);
      await _loadProducts();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Product deleted successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting product: $e')),
      );
    }
  }

  Future<void> _showAddStockDialog(Product product) async {
    final quantityController = TextEditingController();
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Add Stock'),
          content: TextFormField(
            controller: quantityController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Quantity to Add',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value?.isEmpty ?? true) return 'Please enter quantity';
              if (int.tryParse(value!) == null) return 'Invalid quantity';
              return null;
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (int.tryParse(quantityController.text) != null) {
                  final newQuantity = product.quantity + int.parse(quantityController.text);
                  await _updateProduct(product.copyWith(quantity: newQuantity));
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _clearForm() {
    _nameController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _quantityController.clear();
    _costController.clear();
    _barcodeController.clear();
    _isTaxed = false;
    _taxRate = 0.0;
  }

  void _filterProducts(String query) {
    setState(() {
      _filteredProducts = _products.where((product) {
        return product.name.toLowerCase().contains(query.toLowerCase()) ||
            product.description.toLowerCase().contains(query.toLowerCase()) ||
            product.batchNumber.contains(query) ||
            (product.barcode != null && product.barcode!.contains(query));
      }).toList();
    });
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      await _loadSales();
    }
  }

  void _toggleTheme() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
  }

Future<void> _printSales() async {
  // Simplified print method using new PrinterService
  if (_sales.isEmpty) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('No sales to print')),
    );
    return;
  }

  final StringBuffer buffer = StringBuffer();

  // Report Title
  buffer.writeln('SALES SUMMARY REPORT');
  buffer.writeln('Date Range: ${_startDate.toString().substring(0, 10)} to ${_endDate.toString().substring(0, 10)}');
  buffer.writeln('========================================');

  double totalSales = 0.0;
  double totalVAT = 0.0;

  // Latest sales at the top
  final sortedSales = _sales.reversed.toList();

  for (final sale in sortedSales) {
    buffer.writeln('Receipt#: ${sale.receiptNumber}');
    buffer.writeln('Date: ${sale.dateTime.toString().substring(0, 16)}');
    buffer.writeln('Cashier: ${sale.cashierName}');
    buffer.writeln('----------------------------------------');

    // Item list
    buffer.writeln('Items Sold:');
    for (final item in sale.items) {
      buffer.writeln('${item.productName}');
      buffer.writeln('  ${item.quantity} x \$${item.price.toStringAsFixed(2)} = \$${item.total.toStringAsFixed(2)}');
    }

    // Totals for this sale
    buffer.writeln('----------------------------------------');
    buffer.writeln('Subtotal: \$${sale.subtotal.toStringAsFixed(2)}');
    buffer.writeln('VAT: \$${sale.vatTotal.toStringAsFixed(2)}');
    buffer.writeln('Total: \$${sale.total.toStringAsFixed(2)}');
    buffer.writeln('Cash Received: \$${sale.cashReceived.toStringAsFixed(2)}');
    buffer.writeln('----------------------------------------\n');

    totalSales += sale.total;
    totalVAT += sale.vatTotal;
  }

  // Final Summary Totals
  buffer.writeln('========================================');
  buffer.writeln('TOTAL VAT: \$${totalVAT.toStringAsFixed(2)}');
  buffer.writeln('TOTAL SALES: \$${totalSales.toStringAsFixed(2)}');
  buffer.writeln('========================================');

  buffer.writeln('\nPOS developed by AllanWebApp-Cybrox');
  buffer.writeln('\n');

   // Add extra blank lines to force space at the bottom
  for (int i = 0; i < 6; i++) {
    buffer.writeln('');
  }

  // Add ESC/POS command to cut paper
  buffer.writeCharCode(0x1D); // ESC/POS command for cut
  buffer.writeCharCode(0x56); // Cut command
  buffer.writeCharCode(0x00); // Full cut

  // Send to selected printer
  try {
    if (Platform.isAndroid) {
      final prefs = await SharedPreferences.getInstance();
      final printerName = prefs.getString('selected_printer');
      try {
        await SunmiPrinter.bindingPrinter();
        await SunmiPrinter.printText(buffer.toString());
        await SunmiPrinter.cutPaper();
        await SunmiPrinter.bindingPrinter();
        return;
      } catch (e) {
        // Fallback to Bluetooth printer
        final BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
        
        // Convert text to bytes (UTF-8 encoding)
        final List<int> bytes = utf8.encode(buffer.toString());
        
        // Add line feed and carriage return at the end
        bytes.addAll([0x0A, 0x0D]);
        
        // Add paper cut command (ESC m)
        bytes.addAll([0x1B, 0x6D]);
        
        try {
          // Check if Bluetooth is enabled
          bool? isOn = await bluetooth.isOn;
          if (isOn != true) {
            debugPrint('Bluetooth is not enabled');
            return;
          }
          
          // Get bonded devices
          List<BluetoothDevice> bondedDevices = [];
          try {
            bondedDevices = await bluetooth.getBondedDevices();
          } catch (e) {
            debugPrint('Error getting bonded devices: $e');
          }
          
          if (bondedDevices.isEmpty) {
            debugPrint('No bonded Bluetooth devices found');
            return;
          }
          
          // Try to find the saved printer by name or address
          BluetoothDevice? selectedPrinter;
          for (var device in bondedDevices) {
            if (device.name == printerName || device.address == printerName) {
              selectedPrinter = device;
              break;
            }
          }
          
          // If no saved printer found, use the first available one
          final printer = selectedPrinter ?? bondedDevices.first;
          
          // Connect to the printer
          await bluetooth.connect(printer);
          
          try {
            // Send the data
            await bluetooth.writeBytes(Uint8List.fromList(bytes));
          } finally {
            // Always disconnect after sending or if there's an error
            await bluetooth.disconnect();
          }
        } catch (e) {
          debugPrint('Error during Bluetooth printing: $e');
          try {
            await bluetooth.disconnect();
          } catch (_) {}
        }
      }
    } else if (Platform.isWindows) {
      final prefs = await SharedPreferences.getInstance();
      final printerName = prefs.getString('selected_printer') ?? 'POS80 Printer';
      await _directPrintToPOS80WithName(buffer.toString(), printerName);
      return;
    } else {
      debugPrint('Printing is only supported on Android (Sunmi/Bluetooth) and Windows for now.');
    }
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Error printing sales report: $e')),
    );
  }
}

// Add this helper for Windows printing with selected printer name
Future<void> _directPrintToPOS80WithName(String receiptText, String printerName) async {
  final hPrinter = calloc<HANDLE>();
  final docInfo = calloc<DOC_INFO_1>()
    ..ref.pDocName = 'POS Receipt'.toNativeUtf16()
    ..ref.pOutputFile = nullptr
    ..ref.pDatatype = 'RAW'.toNativeUtf16();

  final printerOpen = OpenPrinter(printerName.toNativeUtf16(), hPrinter, nullptr);
  if (printerOpen == 0) {
    debugPrint('Failed to open printer $printerName: \\${GetLastError()}');
    calloc.free(hPrinter);
    calloc.free(docInfo);
    return;
  }

  if (StartDocPrinter(hPrinter.value, 1, docInfo.cast()) == 0) {
    debugPrint('Failed to start document: \\${GetLastError()}');
    ClosePrinter(hPrinter.value);
    calloc.free(hPrinter);
    calloc.free(docInfo);
    return;
  }

  StartPagePrinter(hPrinter.value);

  final receiptBytes = Uint8List.fromList(receiptText.codeUnits);
  final pBytes = calloc<Uint8>(receiptBytes.length);
  pBytes.asTypedList(receiptBytes.length).setAll(0, receiptBytes);

  final bytesWritten = calloc<DWORD>();
  WritePrinter(hPrinter.value, pBytes.cast(), receiptBytes.length, bytesWritten);

  EndPagePrinter(hPrinter.value);
  EndDocPrinter(hPrinter.value);
  ClosePrinter(hPrinter.value);

  calloc.free(pBytes);
  calloc.free(bytesWritten);
  calloc.free(hPrinter);
  calloc.free(docInfo);
}

String _formatDateTime(DateTime dt) {
  return '${dt.year}-${_twoDigits(dt.month)}-${_twoDigits(dt.day)} ${_twoDigits(dt.hour)}:${_twoDigits(dt.minute)}';
}

String _twoDigits(int n) => n.toString().padLeft(2, '0');


// Helper to center text in 32-character wide receipt
String centerText(String text, int width) {
  final space = ((width - text.length) / 2).floor();
  return ' ' * space + text;
}


// Platform Specific Printing (Sunmi, Bluetooth, Windows PowerShell Fallback)
Future<void> _sendToPrinter(String receipt) async {
  try {
    // First try Sunmi printer if available
    await SunmiPrinter.bindingPrinter();
    await SunmiPrinter.printText(receipt);
    await SunmiPrinter.cutPaper();
    await SunmiPrinter.bindingPrinter();
  } catch (e) {
    debugPrint('Sunmi printer error: $e');
    
    // Fallback to Bluetooth printing
    try {
      // Check if Bluetooth is available
      bool isAvailable = await FlutterBluePlus.isAvailable;
      if (!isAvailable) {
        throw Exception('Bluetooth is not available');
      }

      debugPrint('Starting Bluetooth scan...');
      bool isScanning = false;
      
      // Start scanning for devices
      await FlutterBluePlus.startScan(timeout: const Duration(seconds: 4));
      isScanning = true;
      
      // Listen to scan results
      var subscription = FlutterBluePlus.scanResults.listen((results) async {
        for (var result in results) {
          debugPrint('Found device: ${result.device.platformName} (${result.device.remoteId})');
          // Try to connect to any device that looks like a printer
          if (result.device.platformName.isNotEmpty) {
            try {
              await _connectAndPrint(result.device, receipt);
              await FlutterBluePlus.stopScan();
              isScanning = false;
              return;
            } catch (e) {
              debugPrint('Failed to print to ${result.device.platformName}: $e');
            }
          }
        }
      });

      // Stop scanning after delay if still scanning
      await Future.delayed(const Duration(seconds: 4));
      if (isScanning) {
        await FlutterBluePlus.stopScan();
      }
      await subscription.cancel();
      
    } catch (e) {
      debugPrint('Bluetooth printing error: $e');
      
      // Fallback to system printing dialog
      try {
        await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async {
            final doc = pw.Document();
            doc.addPage(
              pw.Page(
                build: (pw.Context context) {
                  return pw.Center(
                    child: pw.Text(receipt),
                  );
                },
              ),
            );
            return doc.save();
          },
        );
      } catch (e) {
        debugPrint('System printing error: $e');
        rethrow;
      }
    }
  }
}

// Connect to Bluetooth device and send print job
Future<void> _connectAndPrint(BluetoothDevice device, String receipt) async {
  try {
    debugPrint('Connecting to device: ${device.platformName} (${device.remoteId})');
    
    // Connect to the device with a timeout
    await device.connect(autoConnect: false, timeout: const Duration(seconds: 5));
    
    // Wait for connection to be established
    await device.connectionState
        .where((state) => state == BluetoothConnectionState.connected)
        .first;
    
    debugPrint('Connected, discovering services...');
    
    // Discover services with a timeout
    List<BluetoothService> services = [];
    try {
      services = await device.discoverServices().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException('Service discovery timed out');
        },
      );
    } catch (e) {
      debugPrint('Service discovery error: $e');
      rethrow;
    }
    
    debugPrint('Discovered ${services.length} services');
    
    // Find the printer service (typically uses SPP - Serial Port Profile)
    for (var service in services) {
      debugPrint('Service: ${service.uuid}');
      
      // Standard SPP service UUID
      if (service.uuid.toString().toLowerCase() == '00001101-0000-1000-8000-00805f9b34fb') {
        for (var characteristic in service.characteristics) {
          debugPrint('Characteristic: ${characteristic.uuid}');
          
          if (characteristic.properties.write) {
            debugPrint('Found writable characteristic');
            
            // Convert text to bytes (using ASCII encoding)
            List<int> bytes = receipt.codeUnits;
            
            // Add line feed and carriage return at the end
            bytes.addAll([0x0A, 0x0D]);
            
            // Send the data to the printer in chunks to avoid MTU issues
            const chunkSize = 20; // Adjust based on your printer's MTU
            for (var i = 0; i < bytes.length; i += chunkSize) {
              var end = (i + chunkSize < bytes.length) ? i + chunkSize : bytes.length;
              var chunk = bytes.sublist(i, end);
              await characteristic.write(chunk);
              await Future.delayed(const Duration(milliseconds: 10));
            }
            
            debugPrint('Print job sent successfully');
            
            // Add a small delay to ensure the data is sent
            await Future.delayed(const Duration(milliseconds: 500));
            
            // Disconnect after printing
            await device.disconnect();
            debugPrint('Disconnected from printer');
            return;
          }
        }
      }
    }
    
    // If we get here, we couldn't find a suitable characteristic
    await device.disconnect();
    throw Exception('No writable characteristic found for printing');
  } catch (e) {
    debugPrint('Print error: $e');
    // Make sure to disconnect if there's an error
    try {
      await device.disconnect();
    } catch (disconnectError) {
      debugPrint('Error disconnecting: $disconnectError');
    }
    rethrow;
  }
}

  @override
  Widget build(BuildContext context) {
    final theme = _isDarkMode
        ? ThemeData.dark().copyWith(
            primaryColor: Colors.blueGrey,
            colorScheme: ColorScheme.dark(secondary: Colors.blueAccent),
            scaffoldBackgroundColor: Colors.grey[900],
          )
        : ThemeData.light().copyWith(
            primaryColor: Colors.blue,
            colorScheme: ColorScheme.light(secondary: Colors.lightBlueAccent),
            scaffoldBackgroundColor: Colors.grey[100],
          );

    return MaterialApp(
      theme: theme,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Products Management'),
          actions: [
            IconButton(
              icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
              onPressed: _toggleTheme,
            ),
          ],
        ),
        body: _buildProductsTab(),
        floatingActionButton: FloatingActionButton(
          onPressed: _showAddProductDialog,
          child: const Icon(Icons.add),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
      ),
    );
  }

  Widget _buildProductsTab() {
    final lowStock = _products.where((p) => p.quantity > 0 && p.quantity < 5).toList();
    final outOfStock = _products.where((p) => p.quantity == 0).toList();
    return Column(
      children: [
        if (lowStock.isNotEmpty || outOfStock.isNotEmpty)
          Padding(
            padding: const EdgeInsets.all(4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (lowStock.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    margin: const EdgeInsets.only(bottom: 2),
                    decoration: BoxDecoration(
                      color: Colors.orange[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Low Stock: ${lowStock.map((p) => p.name).join(", ")}',
                      style: const TextStyle(fontSize: 12, color: Colors.orange, fontWeight: FontWeight.bold),
                    ),
                  ),
                if (outOfStock.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    decoration: BoxDecoration(
                      color: Colors.red[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Out of Stock: ${outOfStock.map((p) => p.name).join(", ")}',
                      style: const TextStyle(fontSize: 12, color: Colors.red, fontWeight: FontWeight.bold),
                    ),
                  ),
              ],
            ),
          ),
        Expanded(
          child: Card(
            margin: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Products Inventory',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(fontSize: 14),
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 120,
                        child: TextField(
                          controller: _searchController,
                          decoration: const InputDecoration(
                            hintText: 'Search...',
                            prefixIcon: Icon(Icons.search, size: 16),
                            border: OutlineInputBorder(),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 13),
                          onChanged: _filterProducts,
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.add, size: 18),
                        onPressed: _showAddProductDialog,
                        tooltip: 'Add New Product',
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                Expanded(
                  child: _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: DataTable(
                            columnSpacing: 8,
                            headingRowHeight: 28,
                            dataRowHeight: 28,
                            columns: const [
                              DataColumn(label: Text('#', style: TextStyle(fontSize: 12))),
                              DataColumn(label: Text('Product', style: TextStyle(fontSize: 12))),
                              DataColumn(label: Text('Desc', style: TextStyle(fontSize: 12))),
                              DataColumn(label: Text('Price', style: TextStyle(fontSize: 12))),
                              DataColumn(label: Text('Cost', style: TextStyle(fontSize: 12))),
                              DataColumn(label: Text('Stock', style: TextStyle(fontSize: 12))),
                              DataColumn(label: Text('Batch', style: TextStyle(fontSize: 12))),
                              DataColumn(label: Text('Tax', style: TextStyle(fontSize: 12))),
                              DataColumn(label: Text('Rate', style: TextStyle(fontSize: 12))),
                              DataColumn(label: Text('Actions', style: TextStyle(fontSize: 12))),
                            ],
                            rows: _filteredProducts.asMap().entries.map((entry) {
                              final index = entry.key;
                              final product = entry.value;
                              return DataRow(
                                cells: [
                                  DataCell(Text('${index + 1}', style: const TextStyle(fontSize: 11))),
                                  DataCell(Text(product.name, style: const TextStyle(fontSize: 11))),
                                  DataCell(Text(product.description, style: const TextStyle(fontSize: 11))),
                                  DataCell(Text('${product.price.toStringAsFixed(2)}', style: const TextStyle(fontSize: 11))),
                                  DataCell(Text('${product.cost.toStringAsFixed(2)}', style: const TextStyle(fontSize: 11))),
                                  DataCell(Text('${product.quantity}', style: const TextStyle(fontSize: 11))),
                                  DataCell(Text(product.batchNumber, style: const TextStyle(fontSize: 11))),
                                  DataCell(Icon(
                                    product.isTaxed ? Icons.check_circle : Icons.cancel,
                                    color: product.isTaxed ? Colors.green : Colors.red,
                                    size: 16,
                                  )),
                                  DataCell(Text('${product.taxRate}%', style: const TextStyle(fontSize: 11))),
                                  DataCell(Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.add_circle_outline, size: 16),
                                        color: Colors.blue,
                                        onPressed: () => _showAddStockDialog(product),
                                        tooltip: 'Add Stock',
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.edit, size: 16),
                                        color: Colors.orange,
                                        onPressed: () => _showEditProductDialog(product),
                                        tooltip: 'Edit Product',
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.delete_outline, size: 16),
                                        color: Colors.red,
                                        onPressed: () => _deleteProduct(product),
                                        tooltip: 'Delete Product',
                                      ),
                                    ],
                                  )),
                                ],
                              );
                            }).toList(),
                          ),
                        ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _showAddProductDialog() async {
    // Check if in trial mode and limit to 5 products
    final prefs = await SharedPreferences.getInstance();
    final installDateStr = prefs.getString('install_date');
    final deviceRegistered = prefs.getBool('device_registered') ?? false;
    bool inTrial = false;
    if (!deviceRegistered && installDateStr != null) {
      final installDate = DateTime.tryParse(installDateStr);
      if (installDate != null) {
        final now = DateTime.now();
        final trialHours = now.difference(installDate).inHours;
        if (trialHours < 72) inTrial = true;
      }
    }
    if (inTrial && _products.length >= 5) {
      showDialog<void>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Trial Limit Reached'),
          content: const Text('You can only add up to 5 products in trial mode. Buy a license to unlock unlimited products and all features.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
      return;
    }
    _costController.clear();
    _taxRate = 0.0;
    _barcodeController.clear();
    bool localIsTaxed = _isTaxed;
    double localTaxRate = _taxRate;
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setLocalState) {
            return AlertDialog(
              title: const Text('Add New Product'),
              content: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Product Name',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        validator: (value) =>
                            value?.isEmpty ?? true ? 'Please enter product name' : null,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        validator: (value) =>
                            value?.isEmpty ?? true ? 'Please enter description' : null,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _priceController,
                        decoration: const InputDecoration(
                          labelText: 'Price',
                          border: OutlineInputBorder(),
                          prefixText: '\$',
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Please enter price';
                          if (double.tryParse(value!) == null) return 'Invalid price';
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _costController,
                        decoration: const InputDecoration(
                          labelText: 'Cost',
                          border: OutlineInputBorder(),
                          prefixText: '\$',
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Please enter cost';
                          if (double.tryParse(value!) == null) return 'Invalid cost';
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _quantityController,
                        decoration: const InputDecoration(
                          labelText: 'Quantity',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Please enter quantity';
                          if (double.tryParse(value!) == null) return 'Invalid quantity';
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _barcodeController,
                        decoration: const InputDecoration(
                          labelText: 'Barcode (EAN-8 or EAN-13)',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        validator: (value) {
                          if (value == null || value.isEmpty) return null;
                          if (!_isValidBarcode(value)) return 'Invalid EAN-8 or EAN-13 barcode';
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      CheckboxListTile(
                        title: const Text('Apply Tax', style: TextStyle(fontSize: 13)),
                        value: localIsTaxed,
                        onChanged: (value) {
                          setLocalState(() => localIsTaxed = value ?? false);
                        },
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                      ),
                      if (localIsTaxed)
                        DropdownButtonFormField<double>(
                          value: localTaxRate,
                          items: [
                            0.0,
                            7.5,
                            10.0,
                            15.0,
                          ].map((rate) {
                            return DropdownMenuItem<double>(
                              value: rate,
                              child: Row(
                                children: [
                                  if (localTaxRate == rate)
                                    const Icon(Icons.check, color: Colors.green, size: 16)
                                  else
                                    const SizedBox(width: 16),
                                  const SizedBox(width: 6),
                                  Text(
                                    '${rate.toStringAsFixed(1)}%',
                                    style: const TextStyle(fontSize: 13, color: Colors.black),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setLocalState(() => localTaxRate = value ?? 0.0);
                          },
                          decoration: const InputDecoration(
                            labelText: 'Tax Rate',
                            border: OutlineInputBorder(),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 13, color: Colors.black),
                        ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () async {
                    if (_formKey.currentState!.validate()) {
                      String barcode = _barcodeController.text.trim();
                      if (barcode.isEmpty) {
                        barcode = _generateEAN13();
                      }
                      final product = Product(
                        name: _nameController.text,
                        description: _descriptionController.text,
                        price: double.parse(_priceController.text),
                        cost: double.parse(_costController.text),
                        quantity: double.parse(_quantityController.text),
                        isTaxed: localIsTaxed,
                        batchNumber: Product.generateBatchNumber(),
                        taxRate: localIsTaxed ? localTaxRate : 0.0,
                        barcode: barcode,
                      );
                      await _db.insertProduct(product);
                      await _loadProducts();
                      Navigator.of(context).pop();
                      _clearForm();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Product saved successfully')),
                      );
                    }
                  },
                  child: const Text('Add Product'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _showEditProductDialog(Product product) async {
    _nameController.text = product.name;
    _descriptionController.text = product.description;
    _priceController.text = product.price.toString();
    _costController.text = product.cost.toString();
    _quantityController.text = product.quantity.toString();
    _isTaxed = product.isTaxed;
    _taxRate = product.taxRate;
    _barcodeController.text = product.barcode ?? '';
    bool localIsTaxed = _isTaxed;
    double localTaxRate = _taxRate;
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setLocalState) {
            return AlertDialog(
              title: const Text('Edit Product'),
              content: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Product Name',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        validator: (value) =>
                            value?.isEmpty ?? true ? 'Please enter product name' : null,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        validator: (value) =>
                            value?.isEmpty ?? true ? 'Please enter description' : null,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _priceController,
                        decoration: const InputDecoration(
                          labelText: 'Price',
                          border: OutlineInputBorder(),
                          prefixText: '\$',
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Please enter price';
                          if (double.tryParse(value!) == null) return 'Invalid price';
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _costController,
                        decoration: const InputDecoration(
                          labelText: 'Cost',
                          border: OutlineInputBorder(),
                          prefixText: '\$',
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Please enter cost';
                          if (double.tryParse(value!) == null) return 'Invalid cost';
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _quantityController,
                        decoration: const InputDecoration(
                          labelText: 'Quantity',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Please enter quantity';
                          if (double.tryParse(value!) == null) return 'Invalid quantity';
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _barcodeController,
                        decoration: const InputDecoration(
                          labelText: 'Barcode (EAN-8 or EAN-13)',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        style: const TextStyle(fontSize: 13),
                        validator: (value) {
                          if (value == null || value.isEmpty) return null;
                          if (!_isValidBarcode(value)) return 'Invalid EAN-8 or EAN-13 barcode';
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      CheckboxListTile(
                        title: const Text('Apply Tax', style: TextStyle(fontSize: 13)),
                        value: localIsTaxed,
                        onChanged: (value) {
                          setLocalState(() => localIsTaxed = value ?? false);
                        },
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                      ),
                      if (localIsTaxed)
                        DropdownButtonFormField<double>(
                          value: localTaxRate,
                          items: [
                            0.0,
                            7.5,
                            10.0,
                            15.0,
                          ].map((rate) {
                            return DropdownMenuItem<double>(
                              value: rate,
                              child: Row(
                                children: [
                                  if (localTaxRate == rate)
                                    const Icon(Icons.check, color: Colors.green, size: 16)
                                  else
                                    const SizedBox(width: 16),
                                  const SizedBox(width: 6),
                                  Text(
                                    '${rate.toStringAsFixed(1)}%',
                                    style: const TextStyle(fontSize: 13, color: Colors.black),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setLocalState(() => localTaxRate = value ?? 0.0);
                          },
                          decoration: const InputDecoration(
                            labelText: 'Tax Rate',
                            border: OutlineInputBorder(),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 13, color: Colors.black),
                        ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () async {
                    if (_formKey.currentState!.validate()) {
                      String barcode = _barcodeController.text.trim();
                      if (barcode.isEmpty) {
                        barcode = _generateEAN13();
                      }
                      final updatedProduct = product.copyWith(
                        name: _nameController.text,
                        description: _descriptionController.text,
                        price: double.parse(_priceController.text),
                        cost: double.parse(_costController.text),
                        quantity: double.parse(_quantityController.text),
                        isTaxed: localIsTaxed,
                        taxRate: localIsTaxed ? localTaxRate : 0.0,
                        barcode: barcode,
                      );
                      await _db.updateProduct(updatedProduct);
                      _clearForm();
                      await _loadProducts();
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Product updated successfully')),
                      );
                    }
                  },
                  child: const Text('Update Product'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  bool _isValidBarcode(String value) {
    final ean8 = RegExp(r'^\d{8}$');
    final ean13 = RegExp(r'^\d{13}$');
    return ean8.hasMatch(value) || ean13.hasMatch(value);
  }

  String _generateEAN13() {
    // Generate a random 12-digit number and calculate the EAN-13 check digit
    final random = DateTime.now().millisecondsSinceEpoch.remainder(1000000000000).toString().padLeft(12, '0');
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      int digit = int.parse(random[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    int checkDigit = (10 - (sum % 10)) % 10;
    return random + checkDigit.toString();
  }
}