1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.allanwebapp_mobile_pos"
4    android:versionCode="1"
5    android:versionName="1.0.0+1" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Internet permissions -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:3:5-67
11-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:4:5-79
12-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:4:22-76
13
14    <!-- Bluetooth permissions -->
15    <uses-permission android:name="android.permission.BLUETOOTH" />
15-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:7:5-68
15-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:7:22-65
16    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
16-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:8:5-74
16-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:8:22-71
17    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
17-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:9:5-76
17-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:9:22-73
18    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
18-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:10:5-73
18-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:10:22-70
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:11:5-79
19-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:11:22-76
20    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
20-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:12:5-81
20-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:12:22-78
21
22    <!-- Camera permissions for barcode scanning -->
23    <uses-permission android:name="android.permission.CAMERA" />
23-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:15:5-65
23-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:15:22-62
24
25    <!-- Storage permissions for database and files -->
26    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
26-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:18:5-81
26-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:18:22-78
27    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
27-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:19:5-80
27-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:19:22-77
28
29    <!-- Wake lock for printing operations -->
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:22:5-68
30-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:22:22-65
31
32    <!-- Feature declarations -->
33    <uses-feature
33-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:25:5-88
34        android:name="android.hardware.bluetooth"
34-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:25:19-60
35        android:required="false" />
35-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:25:61-85
36    <uses-feature
36-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:26:5-85
37        android:name="android.hardware.camera"
37-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:26:19-57
38        android:required="false" />
38-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:26:58-82
39    <uses-feature
39-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:27:5-95
40        android:name="android.hardware.camera.autofocus"
40-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:27:19-67
41        android:required="false" />
41-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:27:68-92
42    <!--
43         Required to query activities that can process text, see:
44         https://developer.android.com/training/package-visibility and
45         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
46
47         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
48    -->
49    <queries>
49-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:66:5-71:15
50        <intent>
50-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:67:9-70:18
51            <action android:name="android.intent.action.PROCESS_TEXT" />
51-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:68:13-72
51-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:68:21-70
52
53            <data android:mimeType="text/plain" />
53-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:69:13-50
53-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:69:19-48
54        </intent>
55
56        <package android:name="woyou.aidlservice.jiuiv5" />
56-->[com.sunmi:printerx:1.0.17] C:\Users\<USER>\.gradle\caches\8.12\transforms\01ed85d65edb07e3a8d338b1bf408670\transformed\jetified-printerx-1.0.17\AndroidManifest.xml:10:9-60
56-->[com.sunmi:printerx:1.0.17] C:\Users\<USER>\.gradle\caches\8.12\transforms\01ed85d65edb07e3a8d338b1bf408670\transformed\jetified-printerx-1.0.17\AndroidManifest.xml:10:18-57
57    </queries>
58
59    <permission
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
60        android:name="com.example.allanwebapp_mobile_pos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
60-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
61        android:protectionLevel="signature" />
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
62
63    <uses-permission android:name="com.example.allanwebapp_mobile_pos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
64
65    <application
66        android:name="android.app.Application"
66-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:31:9-42
67        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
68        android:extractNativeLibs="true"
69        android:icon="@mipmap/ic_launcher"
69-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:32:9-43
70        android:label="allanwebapp_mobile" >
70-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:30:9-43
71        <activity
71-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:33:9-54:20
72            android:name="com.example.allanwebapp_mobile_pos.MainActivity"
72-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:34:13-41
73            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
73-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:39:13-163
74            android:exported="true"
74-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:35:13-36
75            android:hardwareAccelerated="true"
75-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:40:13-47
76            android:launchMode="singleTop"
76-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:36:13-43
77            android:taskAffinity=""
77-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:37:13-36
78            android:theme="@style/LaunchTheme"
78-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:38:13-47
79            android:windowSoftInputMode="adjustResize" >
79-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:41:13-55
80
81            <!--
82                 Specifies an Android theme to apply to this Activity as soon as
83                 the Android process has started. This theme is visible to the user
84                 while the Flutter UI initializes. After that, this theme continues
85                 to determine the Window background behind the Flutter UI.
86            -->
87            <meta-data
87-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:46:13-49:17
88                android:name="io.flutter.embedding.android.NormalTheme"
88-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:47:15-70
89                android:resource="@style/NormalTheme" />
89-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:48:15-52
90
91            <intent-filter>
91-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:50:13-53:29
92                <action android:name="android.intent.action.MAIN" />
92-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:51:17-68
92-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:51:25-66
93
94                <category android:name="android.intent.category.LAUNCHER" />
94-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:52:17-76
94-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:52:27-74
95            </intent-filter>
96        </activity>
97        <!--
98             Don't delete the meta-data below.
99             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
100        -->
101        <meta-data
101-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:57:9-59:33
102            android:name="flutterEmbedding"
102-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:58:13-44
103            android:value="2" />
103-->F:\cybrox_pos_lite\allanwebapp_mobile\allanwebapp_mobile\android\app\src\main\AndroidManifest.xml:59:13-30
104
105        <provider
105-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-19:20
106            android:name="com.crazecoder.openfile.FileProvider"
106-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
107            android:authorities="com.example.allanwebapp_mobile_pos.fileProvider.com.crazecoder.openfile"
107-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-88
108            android:exported="false"
108-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
109            android:grantUriPermissions="true"
109-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
110            android:requestLegacyExternalStorage="true" >
110-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-56
111            <meta-data
111-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-18:53
112                android:name="android.support.FILE_PROVIDER_PATHS"
112-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:17-67
113                android:resource="@xml/filepaths" />
113-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:17-50
114        </provider>
115        <provider
115-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
116            android:name="net.nfet.flutter.printing.PrintFileProvider"
116-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-71
117            android:authorities="com.example.allanwebapp_mobile_pos.flutter.printing"
117-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-68
118            android:exported="false"
118-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
119            android:grantUriPermissions="true" >
119-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
120            <meta-data
120-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-18:53
121                android:name="android.support.FILE_PROVIDER_PATHS"
121-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:17-67
122                android:resource="@xml/flutter_printing_file_paths" />
122-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:17-50
123        </provider>
124        <!--
125        Service for holding metadata. Cannot be instantiated.
126        Metadata will be merged from other manifests.
127        -->
128        <service
128-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d88581e4d1ac1a8317277d9ad2571051\transformed\jetified-camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
129            android:name="androidx.camera.core.impl.MetadataHolderService"
129-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d88581e4d1ac1a8317277d9ad2571051\transformed\jetified-camera-core-1.3.1\AndroidManifest.xml:30:13-75
130            android:enabled="false"
130-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d88581e4d1ac1a8317277d9ad2571051\transformed\jetified-camera-core-1.3.1\AndroidManifest.xml:31:13-36
131            android:exported="false" >
131-->[androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d88581e4d1ac1a8317277d9ad2571051\transformed\jetified-camera-core-1.3.1\AndroidManifest.xml:32:13-37
132            <meta-data
132-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\47e500e8ebd69d83ec39d22e169b20d8\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
133                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
133-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\47e500e8ebd69d83ec39d22e169b20d8\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
134                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
134-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\47e500e8ebd69d83ec39d22e169b20d8\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
135        </service>
136
137        <provider
137-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
138            android:name="androidx.startup.InitializationProvider"
138-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
139            android:authorities="com.example.allanwebapp_mobile_pos.androidx-startup"
139-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
140            android:exported="false" >
140-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
141            <meta-data
141-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
142                android:name="androidx.emoji2.text.EmojiCompatInitializer"
142-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
143                android:value="androidx.startup" />
143-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
144            <meta-data
144-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
145                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
145-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
146                android:value="androidx.startup" />
146-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
147            <meta-data
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
148                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
149                android:value="androidx.startup" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
150        </provider>
151
152        <uses-library
152-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
153            android:name="androidx.window.extensions"
153-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
154            android:required="false" />
154-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
155        <uses-library
155-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
156            android:name="androidx.window.sidecar"
156-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
157            android:required="false" />
157-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
158
159        <service
159-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37e1752a2fe7ab241b17382eb6675281\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
160            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
160-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37e1752a2fe7ab241b17382eb6675281\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
161            android:directBootAware="true"
161-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f363aaa5f1b2e5d2d117d973b433db8\transformed\jetified-common-18.9.0\AndroidManifest.xml:17:13-43
162            android:exported="false" >
162-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37e1752a2fe7ab241b17382eb6675281\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
163            <meta-data
163-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37e1752a2fe7ab241b17382eb6675281\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
164                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
164-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37e1752a2fe7ab241b17382eb6675281\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37e1752a2fe7ab241b17382eb6675281\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
166            <meta-data
166-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5bb132f9fe0f024fbd8775a173ddb857\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
167                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
167-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5bb132f9fe0f024fbd8775a173ddb857\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5bb132f9fe0f024fbd8775a173ddb857\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
169            <meta-data
169-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f363aaa5f1b2e5d2d117d973b433db8\transformed\jetified-common-18.9.0\AndroidManifest.xml:20:13-22:85
170                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
170-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f363aaa5f1b2e5d2d117d973b433db8\transformed\jetified-common-18.9.0\AndroidManifest.xml:21:17-120
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f363aaa5f1b2e5d2d117d973b433db8\transformed\jetified-common-18.9.0\AndroidManifest.xml:22:17-82
172        </service>
173
174        <provider
174-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f363aaa5f1b2e5d2d117d973b433db8\transformed\jetified-common-18.9.0\AndroidManifest.xml:9:9-13:38
175            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
175-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f363aaa5f1b2e5d2d117d973b433db8\transformed\jetified-common-18.9.0\AndroidManifest.xml:10:13-78
176            android:authorities="com.example.allanwebapp_mobile_pos.mlkitinitprovider"
176-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f363aaa5f1b2e5d2d117d973b433db8\transformed\jetified-common-18.9.0\AndroidManifest.xml:11:13-69
177            android:exported="false"
177-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f363aaa5f1b2e5d2d117d973b433db8\transformed\jetified-common-18.9.0\AndroidManifest.xml:12:13-37
178            android:initOrder="99" />
178-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f363aaa5f1b2e5d2d117d973b433db8\transformed\jetified-common-18.9.0\AndroidManifest.xml:13:13-35
179
180        <activity
180-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
181            android:name="com.google.android.gms.common.api.GoogleApiActivity"
181-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
182            android:exported="false"
182-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
183            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
183-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
184
185        <meta-data
185-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\701989a7f3c365ef1ab8659fa80ae0b8\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
186            android:name="com.google.android.gms.version"
186-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\701989a7f3c365ef1ab8659fa80ae0b8\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
187            android:value="@integer/google_play_services_version" />
187-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\701989a7f3c365ef1ab8659fa80ae0b8\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
188
189        <receiver
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
190            android:name="androidx.profileinstaller.ProfileInstallReceiver"
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
191            android:directBootAware="false"
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
192            android:enabled="true"
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
193            android:exported="true"
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
194            android:permission="android.permission.DUMP" >
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
195            <intent-filter>
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
196                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
197            </intent-filter>
198            <intent-filter>
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
199                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
200            </intent-filter>
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
202                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
203            </intent-filter>
204            <intent-filter>
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
205                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
206            </intent-filter>
207        </receiver>
208
209        <service
209-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\72fb61582bd9f83d336279a04a7593bb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
210            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
210-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\72fb61582bd9f83d336279a04a7593bb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
211            android:exported="false" >
211-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\72fb61582bd9f83d336279a04a7593bb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
212            <meta-data
212-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\72fb61582bd9f83d336279a04a7593bb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
213                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
213-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\72fb61582bd9f83d336279a04a7593bb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
214                android:value="cct" />
214-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\72fb61582bd9f83d336279a04a7593bb\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
215        </service>
216        <service
216-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb2b13e005f0ac5e2119519316b451d\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
217            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
217-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb2b13e005f0ac5e2119519316b451d\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
218            android:exported="false"
218-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb2b13e005f0ac5e2119519316b451d\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
219            android:permission="android.permission.BIND_JOB_SERVICE" >
219-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb2b13e005f0ac5e2119519316b451d\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
220        </service>
221
222        <receiver
222-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb2b13e005f0ac5e2119519316b451d\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
223            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
223-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb2b13e005f0ac5e2119519316b451d\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
224            android:exported="false" />
224-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb2b13e005f0ac5e2119519316b451d\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
225    </application>
226
227</manifest>
