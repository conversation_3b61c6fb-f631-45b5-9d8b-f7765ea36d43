<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2020 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<set xmlns:android="http://schemas.android.com/apk/res/android">
    <set android:ordering="sequentially">
        <objectAnimator
            android:interpolator="@android:anim/linear_interpolator"
            android:propertyName="alpha"
            android:valueFrom="0.0" android:valueTo="0.0"
            android:duration="66" />

        <objectAnimator
            android:interpolator="@android:anim/linear_interpolator"
            android:propertyName="alpha"
            android:valueFrom="0.0" android:valueTo="1.0"
            android:duration="50"/>
    </set>

    <objectAnimator
        android:interpolator="@anim/fragment_fast_out_extra_slow_in"
        android:propertyName="scaleX"
        android:valueFrom="1.1" android:valueTo="1.0"
        android:duration="300"/>

    <objectAnimator
        android:interpolator="@anim/fragment_fast_out_extra_slow_in"
        android:propertyName="scaleY"
        android:valueFrom="1.1" android:valueTo="1.0"
        android:duration="300"/>
</set>
